/**
 * 网易云音乐测试源 - 仅供测试使用
 * 基于 kxzjoker API
 * 禁止用于商业用途！
 */

// 配置信息
const CONFIG = {
    name: "NetEase Music Test Source",
    version: "1.0.1",
    author: "Test Developer",
    description: "网易云音乐测试源，基于kxzjoker API，仅供测试使用",
    supportedQualities: ["standard", "exhigh", "lossless", "hires", "jyeffect", "sky", "jymaster"]
};

// API配置
const API_BASE = "https://api.kxzjoker.cn/api/163_music";

// 音质映射
const QUALITY_MAP = {
    "128k": "standard",
    "320k": "exhigh", 
    "flac": "lossless",
    "hires": "hires",
    "surround": "jyeffect",
    "sky": "sky",
    "master": "jymaster"
};

/**
 * 搜索音乐
 * @param {string} keyword 搜索关键词
 * @param {number} page 页码
 * @param {number} limit 每页数量
 * @returns {Promise<Array>} 搜索结果
 */
async function searchMusic(keyword, page = 1, limit = 20) {
    try {
        console.log(`[NetEase Search] 搜索关键词: ${keyword}, 页码: ${page}, 数量: ${limit}`);

        // 使用网易云音乐搜索API
        const searchUrl = `https://api.kxzjoker.cn/api/163_search?name=${encodeURIComponent(keyword)}&limit=${limit}`;

        const response = await fetch(searchUrl, {
            method: 'GET',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': 'https://music.163.com/'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP请求失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`[NetEase Search] API响应状态: ${data.code}`);

        if (data.code !== 200 || !data.data) {
            throw new Error(`搜索API返回错误: ${data.code}`);
        }

        // 转换搜索结果为标准格式
        const results = data.data.map(item => ({
            id: item.id.toString(),
            name: item.name,
            artist: item.artists.map(artist => artist.name),
            album: item.album.name,
            duration: parseDuration(item.duration),
            pic: `https://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg`, // 默认封面，实际使用时需要获取真实封面
            url: `https://music.163.com/song?id=${item.id}`,
            // 将网易云音乐ID存储在extra字段中，供播放时使用
            extra: JSON.stringify({
                netease_id: item.id,
                album_id: item.album.id,
                artist_ids: item.artists.map(artist => artist.id),
                publish_time: item.album.publishTime
            })
        }));

        console.log(`[NetEase Search] 搜索成功，返回 ${results.length} 个结果`);
        return results;

    } catch (error) {
        console.error("[NetEase Search] 搜索失败:", error);
        return [];
    }
}

/**
 * 解析时长字符串为秒数
 * @param {string} durationStr 时长字符串，如 "03:22"
 * @returns {number} 秒数
 */
function parseDuration(durationStr) {
    if (!durationStr) return 0;

    const parts = durationStr.split(':');
    if (parts.length === 2) {
        const minutes = parseInt(parts[0], 10) || 0;
        const seconds = parseInt(parts[1], 10) || 0;
        return minutes * 60 + seconds;
    } else if (parts.length === 3) {
        const hours = parseInt(parts[0], 10) || 0;
        const minutes = parseInt(parts[1], 10) || 0;
        const seconds = parseInt(parts[2], 10) || 0;
        return hours * 3600 + minutes * 60 + seconds;
    }

    return 0;
}

/**
 * 获取音乐播放信息 - 专注于播放链接解析
 * @param {Object} musicInfo 音乐信息 {id, name, artist, etc.}
 * @param {string} quality 音质 (128k, 320k, flac, etc.)
 * @returns {Promise<Object|null>} 播放信息
 */
async function getPlayInfo(musicInfo, quality = "320k") {
    try {
        console.log(`[NetEase Test] 解析播放链接: ${musicInfo.name || musicInfo.id}, 音质: ${quality}`);

        // 映射音质到API参数
        const apiQuality = QUALITY_MAP[quality] || "exhigh";

        // 获取音乐ID - 优先从extra字段获取真实的网易云音乐ID
        let musicId = null;

        // 1. 尝试从extra字段获取网易云音乐ID
        if (musicInfo.extra) {
            try {
                const extraData = JSON.parse(musicInfo.extra);
                if (extraData.netease_id) {
                    musicId = extraData.netease_id.toString();
                    console.log(`[NetEase Test] 从extra字段获取网易云音乐ID: ${musicId}`);
                }
            } catch (e) {
                console.log(`[NetEase Test] 解析extra字段失败: ${e.message}`);
            }
        }

        // 2. 如果extra字段没有ID，尝试从URL中提取
        if (!musicId && musicInfo.url) {
            const idMatch = musicInfo.url.match(/id[=\/](\d+)/);
            if (idMatch) {
                musicId = idMatch[1];
                console.log(`[NetEase Test] 从URL提取音乐ID: ${musicId}`);
            }
        }

        // 3. 最后尝试使用内部ID（通常无效）
        if (!musicId && musicInfo.id && musicInfo.id !== '0') {
            musicId = musicInfo.id.toString();
            console.log(`[NetEase Test] 使用内部ID: ${musicId}`);
        }

        if (!musicId) {
            throw new Error("无法获取有效的网易云音乐ID");
        }

        // 构建API请求URL
        const requestUrl = `${API_BASE}?ids=${musicId}&level=${apiQuality}&type=json`;
        console.log(`[NetEase Test] 请求URL: ${requestUrl}`);

        // 发送HTTP请求
        const response = await fetch(requestUrl, {
            method: 'GET',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': 'https://music.163.com/'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP请求失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log(`[NetEase Test] API响应状态: ${data.status}`);

        // 检查API响应
        if (data.status !== 200) {
            throw new Error(`API返回错误状态: ${data.status}, 消息: ${data.msg || '未知错误'}`);
        }

        if (!data.url || data.url === null) {
            throw new Error(`无法获取播放链接，可能需要VIP权限或歌曲不存在`);
        }

        console.log(`[NetEase Test] 成功获取播放链接: ${data.url.substring(0, 50)}...`);

        // 返回标准化的播放信息
        return {
            uri: data.url,
            quality: {
                short: quality,
                level: data.level || apiQuality,
                bitrate: getBitrateFromQuality(quality),
                format: getFormatFromUrl(data.url),
                size: data.size
            },
            title: data.name || musicInfo.name,
            artist: data.ar_name || (Array.isArray(musicInfo.artist) ? musicInfo.artist.join(',') : musicInfo.artist),
            album: data.al_name || musicInfo.album,
            pic: data.pic || musicInfo.pic,
            lyric: data.lyric,
            tlyric: data.tlyric
        };

    } catch (error) {
        console.error("[NetEase Test] 播放链接解析失败:", error.message);
        return null;
    }
}

/**
 * 获取歌词
 * @param {Object} musicInfo 音乐信息
 * @returns {Promise<string|null>} 歌词
 */
async function getLyric(musicInfo) {
    try {
        const playInfo = await getPlayInfo(musicInfo, "standard");
        return playInfo ? playInfo.lyric : null;
    } catch (error) {
        console.error("[NetEase Test] 获取歌词失败:", error);
        return null;
    }
}

/**
 * 根据音质获取比特率
 * @param {string} quality 音质
 * @returns {number} 比特率
 */
function getBitrateFromQuality(quality) {
    const bitrateMap = {
        "128k": 128,
        "320k": 320,
        "flac": 1411,
        "hires": 2304,
        "surround": 320,
        "sky": 320,
        "master": 2304
    };
    return bitrateMap[quality] || 320;
}

/**
 * 从URL获取音频格式
 * @param {string} url 音频URL
 * @returns {string} 音频格式
 */
function getFormatFromUrl(url) {
    if (url.includes('.flac')) return 'flac';
    if (url.includes('.mp3')) return 'mp3';
    if (url.includes('.m4a')) return 'm4a';
    return 'mp3';
}

/**
 * 验证音乐源
 * @returns {Promise<boolean>} 是否可用
 */
async function validateSource() {
    try {
        // 测试API是否可用
        const testUrl = `${API_BASE}?ids=475479888&level=standard&type=json`;
        const response = await fetch(testUrl);
        return response.ok;
    } catch (error) {
        console.error("[NetEase Test] 源验证失败:", error);
        return false;
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CONFIG,
        getPlayInfo,
        getLyric,
        validateSource
    };
}

// 浏览器环境
if (typeof window !== 'undefined') {
    window.NetEaseMusicTestSource = {
        CONFIG,
        getPlayInfo,
        getLyric,
        validateSource
    };
}

console.log("[NetEase Test] 网易云音乐测试源已加载");
console.log("[NetEase Test] 配置信息:", CONFIG);
console.log("[NetEase Test] 支持的音质:", CONFIG.supportedQualities);
console.log("[NetEase Test] 警告: 仅供测试使用，禁止商业用途！");
