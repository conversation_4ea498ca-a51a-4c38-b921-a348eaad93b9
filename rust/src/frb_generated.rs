// This file is automatically generated, so please do not edit it.
// Generated by `flutter_rust_bridge`@ 2.0.0.

#![allow(
    non_camel_case_types,
    unused,
    non_snake_case,
    clippy::needless_return,
    clippy::redundant_closure_call,
    clippy::redundant_closure,
    clippy::useless_conversion,
    clippy::unit_arg,
    clippy::unused_unit,
    clippy::double_parens,
    clippy::let_and_return,
    clippy::too_many_arguments,
    clippy::match_single_binding,
    clippy::clone_on_copy,
    clippy::let_unit_value,
    clippy::deref_addrof,
    clippy::explicit_auto_deref,
    clippy::borrow_deref_ref,
    clippy::needless_borrow
)]

// Section: imports

use crate::api::bind::type_bind::*;
use flutter_rust_bridge::for_generated::byteorder::{NativeEndian, ReadBytesExt, WriteBytesExt};
use flutter_rust_bridge::for_generated::{transform_result_dco, Lifetimeable, Lockable};
use flutter_rust_bridge::{<PERSON><PERSON>, IntoIntoDart};

// Section: boilerplate

flutter_rust_bridge::frb_generated_boilerplate!(
    default_stream_sink_codec = SseCodec,
    default_rust_opaque = RustOpaqueMoi,
    default_rust_auto_opaque = RustAutoOpaqueMoi,
);
pub(crate) const FLUTTER_RUST_BRIDGE_CODEGEN_VERSION: &str = "2.0.0";
pub(crate) const FLUTTER_RUST_BRIDGE_CODEGEN_CONTENT_HASH: i32 = 402443197;

// Section: executor

flutter_rust_bridge::frb_generated_default_handler!();

// Section: wire_funcs

fn wire__crate__api__bind__factory_bind__aggregator_online_factory_w_search_music_aggregator_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec,_,_,_>(flutter_rust_bridge::for_generated::TaskInfo{ debug_name: "aggregator_online_factory_w_search_music_aggregator", port: Some(port_), mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal }, move || { 
            let message = unsafe { flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(ptr_, rust_vec_len_, data_len_) };
            let mut deserializer = flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_aggregators = <Vec<MusicAggregatorW>>::sse_decode(&mut deserializer);
let api_sources = <Vec<String>>::sse_decode(&mut deserializer);
let api_content = <String>::sse_decode(&mut deserializer);
let api_page = <u32>::sse_decode(&mut deserializer);
let api_limit = <u32>::sse_decode(&mut deserializer);
let api_filter = <Option<music_api::MusicFuzzFilter>>::sse_decode(&mut deserializer);deserializer.end(); move |context| async move {
                    transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>((move || async move {
                         let output_ok = crate::api::bind::factory_bind::AggregatorOnlineFactoryW::search_music_aggregator(api_aggregators, &api_sources, &api_content, api_page, api_limit, api_filter).await?;  Ok(output_ok)
                    })().await)
                } })
}
fn wire__crate__api__bind__factory_bind__online_factory_w_get_musiclist_from_share_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec,_,_,_>(flutter_rust_bridge::for_generated::TaskInfo{ debug_name: "online_factory_w_get_musiclist_from_share", port: Some(port_), mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal }, move || { 
            let message = unsafe { flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(ptr_, rust_vec_len_, data_len_) };
            let mut deserializer = flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_share_url = <String>::sse_decode(&mut deserializer);deserializer.end(); move |context| async move {
                    transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>((move || async move {
                         let output_ok = crate::api::bind::factory_bind::OnlineFactoryW::get_musiclist_from_share(&api_share_url).await?;  Ok(output_ok)
                    })().await)
                } })
}
fn wire__crate__api__bind__factory_bind__online_factory_w_search_musiclist_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "online_factory_w_search_musiclist",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_sources = <Vec<String>>::sse_decode(&mut deserializer);
            let api_content = <String>::sse_decode(&mut deserializer);
            let api_page = <u32>::sse_decode(&mut deserializer);
            let api_limit = <u32>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::bind::factory_bind::OnlineFactoryW::search_musiclist(
                                api_sources,
                                &api_content,
                                api_page,
                                api_limit,
                            )
                            .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_add_musics_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_add_musics",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_musics_list_name = <String>::sse_decode(&mut deserializer);
            let api_musics = <Vec<MusicAggregatorW>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok = crate::api::bind::factory_bind::SqlFactoryW::add_musics(
                            &api_musics_list_name,
                            &api_musics,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_change_music_default_source_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec,_,_,_>(flutter_rust_bridge::for_generated::TaskInfo{ debug_name: "sql_factory_w_change_music_default_source", port: Some(port_), mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal }, move || { 
            let message = unsafe { flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(ptr_, rust_vec_len_, data_len_) };
            let mut deserializer = flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_music_list_name = <String>::sse_decode(&mut deserializer);
let api_ids = <Vec<i64>>::sse_decode(&mut deserializer);
let api_new_default_sources = <Vec<String>>::sse_decode(&mut deserializer);deserializer.end(); move |context| async move {
                    transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>((move || async move {
                         let output_ok = crate::api::bind::factory_bind::SqlFactoryW::change_music_default_source(&api_music_list_name, api_ids, api_new_default_sources).await?;  Ok(output_ok)
                    })().await)
                } })
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_change_music_info_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_change_music_info",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_musics = <Vec<MusicW>>::sse_decode(&mut deserializer);
            let api_new_infos =
                <Vec<music_api::MusicInfo>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::bind::factory_bind::SqlFactoryW::change_music_info(
                                api_musics,
                                api_new_infos,
                            )
                            .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_change_musiclist_info_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_change_musiclist_info",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_old =
                <Vec<music_api::MusicListInfo>>::sse_decode(&mut deserializer);
            let api_new =
                <Vec<music_api::MusicListInfo>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::bind::factory_bind::SqlFactoryW::change_musiclist_info(
                                &api_old, &api_new,
                            )
                            .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_clean_unused_music_data_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_clean_unused_music_data",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::bind::factory_bind::SqlFactoryW::clean_unused_music_data()
                                .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_clean_unused_musiclist_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_clean_unused_musiclist",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::bind::factory_bind::SqlFactoryW::clean_unused_musiclist()
                                .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_create_musiclist_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_create_musiclist",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_music_list_infos =
                <Vec<music_api::MusicListInfo>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::bind::factory_bind::SqlFactoryW::create_musiclist(
                                &api_music_list_infos,
                            )
                            .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_del_duplicate_musics_of_musiclist_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec,_,_,_>(flutter_rust_bridge::for_generated::TaskInfo{ debug_name: "sql_factory_w_del_duplicate_musics_of_musiclist", port: Some(port_), mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal }, move || { 
            let message = unsafe { flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(ptr_, rust_vec_len_, data_len_) };
            let mut deserializer = flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_musiclist_info = <music_api::MusicListInfo>::sse_decode(&mut deserializer);deserializer.end(); move |context| async move {
                    transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>((move || async move {
                         let output_ok = crate::api::bind::factory_bind::SqlFactoryW::del_duplicate_musics_of_musiclist(&api_musiclist_info).await?;  Ok(output_ok)
                    })().await)
                } })
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_del_musiclist_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_del_musiclist",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_musiclist_names = <Vec<String>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok = crate::api::bind::factory_bind::SqlFactoryW::del_musiclist(
                            &api_musiclist_names,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_del_musics_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_del_musics",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_music_list_name = <String>::sse_decode(&mut deserializer);
            let api_ids = <Vec<i64>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok = crate::api::bind::factory_bind::SqlFactoryW::del_musics(
                            &api_music_list_name,
                            api_ids,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_get_all_musiclists_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_get_all_musiclists",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::bind::factory_bind::SqlFactoryW::get_all_musiclists()
                                .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_get_all_musics_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_get_all_musics",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_musiclist_info =
                <music_api::MusicListInfo>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::bind::factory_bind::SqlFactoryW::get_all_musics(
                                &api_musiclist_info,
                            )
                            .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_get_music_by_id_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_get_music_by_id",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_music_list_info =
                <music_api::MusicListInfo>::sse_decode(&mut deserializer);
            let api_id = <i64>::sse_decode(&mut deserializer);
            let api_sources = <Vec<String>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::bind::factory_bind::SqlFactoryW::get_music_by_id(
                                &api_music_list_info,
                                api_id,
                                &api_sources,
                            )
                            .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_init_from_path_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_init_from_path",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_filepath = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::bind::factory_bind::SqlFactoryW::init_from_path(
                                &api_filepath,
                            )
                            .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_read_music_data_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_read_music_data",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_source = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::bind::factory_bind::SqlFactoryW::read_music_data(
                                &api_source,
                            )
                            .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_reorder_musiclist_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_reorder_musiclist",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_new_ids = <Vec<i64>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::bind::factory_bind::SqlFactoryW::reorder_musiclist(
                                &api_new_ids,
                            )
                            .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_reorder_musics_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_reorder_musics",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_music_list_name = <String>::sse_decode(&mut deserializer);
            let api_new_ids = <Vec<i64>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::bind::factory_bind::SqlFactoryW::reorder_musics(
                                &api_music_list_name,
                                &api_new_ids,
                            )
                            .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_replace_musics_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_replace_musics",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_music_list_name = <String>::sse_decode(&mut deserializer);
            let api_ids = <Vec<i64>>::sse_decode(&mut deserializer);
            let api_musics = <Vec<MusicAggregatorW>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::bind::factory_bind::SqlFactoryW::replace_musics(
                                &api_music_list_name,
                                api_ids,
                                api_musics,
                            )
                            .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__factory_bind__sql_factory_w_shutdown_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "sql_factory_w_shutdown",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::bind::factory_bind::SqlFactoryW::shutdown().await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicAggregatorW_add_music_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicAggregatorW_add_music",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
            >>::sse_decode(&mut deserializer);
            let api_music = <MusicW>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let mut api_that_guard = None;
                        let decode_indices_ =
                            flutter_rust_bridge::for_generated::lockable_compute_decode_order(
                                vec![flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                    &api_that, 0, true,
                                )],
                            );
                        for i in decode_indices_ {
                            match i {
                                0 => {
                                    api_that_guard =
                                        Some(api_that.lockable_decode_async_ref_mut().await)
                                }
                                _ => unreachable!(),
                            }
                        }
                        let mut api_that_guard = api_that_guard.unwrap();
                        let output_ok = crate::api::bind::type_bind::MusicAggregatorW::add_music(
                            &mut *api_that_guard,
                            api_music,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicAggregatorW_belong_to_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicAggregatorW_belong_to",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
            >>::sse_decode(&mut deserializer);
            let api_music = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let mut api_music_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_music, 1, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        1 => api_music_guard = Some(api_music.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let api_music_guard = api_music_guard.unwrap();
                let output_ok =
                    Result::<_, ()>::Ok(crate::api::bind::type_bind::MusicAggregatorW::belong_to(
                        &*api_that_guard,
                        &*api_music_guard,
                    ))?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicAggregatorW_clone_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicAggregatorW_clone",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::api::bind::type_bind::MusicAggregatorW::clone(&*api_that_guard),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicAggregatorW_fetch_album_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicAggregatorW_fetch_album",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
            >>::sse_decode(&mut deserializer);
            let api_page = <u32>::sse_decode(&mut deserializer);
            let api_limit = <u32>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let mut api_that_guard = None;
                        let decode_indices_ =
                            flutter_rust_bridge::for_generated::lockable_compute_decode_order(
                                vec![flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                    &api_that, 0, false,
                                )],
                            );
                        for i in decode_indices_ {
                            match i {
                                0 => {
                                    api_that_guard =
                                        Some(api_that.lockable_decode_async_ref().await)
                                }
                                _ => unreachable!(),
                            }
                        }
                        let api_that_guard = api_that_guard.unwrap();
                        let output_ok = crate::api::bind::type_bind::MusicAggregatorW::fetch_album(
                            &*api_that_guard,
                            api_page,
                            api_limit,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicAggregatorW_fetch_lyric_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicAggregatorW_fetch_lyric",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let mut api_that_guard = None;
                        let decode_indices_ =
                            flutter_rust_bridge::for_generated::lockable_compute_decode_order(
                                vec![flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                    &api_that, 0, false,
                                )],
                            );
                        for i in decode_indices_ {
                            match i {
                                0 => {
                                    api_that_guard =
                                        Some(api_that.lockable_decode_async_ref().await)
                                }
                                _ => unreachable!(),
                            }
                        }
                        let api_that_guard = api_that_guard.unwrap();
                        let output_ok = crate::api::bind::type_bind::MusicAggregatorW::fetch_lyric(
                            &*api_that_guard,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicAggregatorW_fetch_musics_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicAggregatorW_fetch_musics",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
            >>::sse_decode(&mut deserializer);
            let api_sources = <Vec<String>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let mut api_that_guard = None;
                        let decode_indices_ =
                            flutter_rust_bridge::for_generated::lockable_compute_decode_order(
                                vec![flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                    &api_that, 0, true,
                                )],
                            );
                        for i in decode_indices_ {
                            match i {
                                0 => {
                                    api_that_guard =
                                        Some(api_that.lockable_decode_async_ref_mut().await)
                                }
                                _ => unreachable!(),
                            }
                        }
                        let mut api_that_guard = api_that_guard.unwrap();
                        let output_ok =
                            crate::api::bind::type_bind::MusicAggregatorW::fetch_musics(
                                &mut *api_that_guard,
                                api_sources,
                            )
                            .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicAggregatorW_get_all_musics_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicAggregatorW_get_all_musics",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::api::bind::type_bind::MusicAggregatorW::get_all_musics(&*api_that_guard),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicAggregatorW_get_all_musics_owned_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicAggregatorW_get_all_musics_owned",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::api::bind::type_bind::MusicAggregatorW::get_all_musics_owned(
                        &*api_that_guard,
                    ),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicAggregatorW_get_available_sources_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicAggregatorW_get_available_sources",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::api::bind::type_bind::MusicAggregatorW::get_available_sources(
                        &*api_that_guard,
                    ),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicAggregatorW_get_default_music_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicAggregatorW_get_default_music",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::api::bind::type_bind::MusicAggregatorW::get_default_music(
                        &*api_that_guard,
                    ),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicAggregatorW_get_default_source_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicAggregatorW_get_default_source",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::api::bind::type_bind::MusicAggregatorW::get_default_source(
                        &*api_that_guard,
                    ),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicAggregatorW_get_music_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicAggregatorW_get_music",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
            >>::sse_decode(&mut deserializer);
            let api_source = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, ()>(
                    (move || async move {
                        let mut api_that_guard = None;
                        let decode_indices_ =
                            flutter_rust_bridge::for_generated::lockable_compute_decode_order(
                                vec![flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                    &api_that, 0, true,
                                )],
                            );
                        for i in decode_indices_ {
                            match i {
                                0 => {
                                    api_that_guard =
                                        Some(api_that.lockable_decode_async_ref_mut().await)
                                }
                                _ => unreachable!(),
                            }
                        }
                        let mut api_that_guard = api_that_guard.unwrap();
                        let output_ok = Result::<_, ()>::Ok(
                            crate::api::bind::type_bind::MusicAggregatorW::get_music(
                                &mut *api_that_guard,
                                &api_source,
                            )
                            .await,
                        )?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicAggregatorW_get_music_id_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicAggregatorW_get_music_id",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::api::bind::type_bind::MusicAggregatorW::get_music_id(&*api_that_guard),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicAggregatorW_match_filter_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicAggregatorW_match_filter",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
            >>::sse_decode(&mut deserializer);
            let api_filter =
                <music_api::MusicFuzzFilter>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::api::bind::type_bind::MusicAggregatorW::match_filter(
                        &*api_that_guard,
                        &api_filter,
                    ),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicAggregatorW_set_default_source_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicAggregatorW_set_default_source",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
            >>::sse_decode(&mut deserializer);
            let api_source = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let mut api_that_guard = None;
                        let decode_indices_ =
                            flutter_rust_bridge::for_generated::lockable_compute_decode_order(
                                vec![flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                    &api_that, 0, true,
                                )],
                            );
                        for i in decode_indices_ {
                            match i {
                                0 => {
                                    api_that_guard =
                                        Some(api_that.lockable_decode_async_ref_mut().await)
                                }
                                _ => unreachable!(),
                            }
                        }
                        let mut api_that_guard = api_that_guard.unwrap();
                        let output_ok =
                            crate::api::bind::type_bind::MusicAggregatorW::set_default_source(
                                &mut *api_that_guard,
                                &api_source,
                            )
                            .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicAggregatorW_to_string_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicAggregatorW_to_string",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::api::bind::type_bind::MusicAggregatorW::to_string(&*api_that_guard),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicListW_fetch_all_music_aggregators_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicListW_fetch_all_music_aggregators",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicListW>,
            >>::sse_decode(&mut deserializer);
            let api_pages_per_batch = <u32>::sse_decode(&mut deserializer);
            let api_limit = <u32>::sse_decode(&mut deserializer);
            let api_with_lyric = <bool>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let mut api_that_guard = None;
                        let decode_indices_ =
                            flutter_rust_bridge::for_generated::lockable_compute_decode_order(
                                vec![flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                    &api_that, 0, false,
                                )],
                            );
                        for i in decode_indices_ {
                            match i {
                                0 => {
                                    api_that_guard =
                                        Some(api_that.lockable_decode_async_ref().await)
                                }
                                _ => unreachable!(),
                            }
                        }
                        let api_that_guard = api_that_guard.unwrap();
                        let output_ok =
                            crate::api::bind::type_bind::MusicListW::fetch_all_music_aggregators(
                                &*api_that_guard,
                                api_pages_per_batch,
                                api_limit,
                                api_with_lyric,
                            )
                            .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicListW_get_music_aggregators_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicListW_get_music_aggregators",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicListW>,
            >>::sse_decode(&mut deserializer);
            let api_page = <u32>::sse_decode(&mut deserializer);
            let api_limit = <u32>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let mut api_that_guard = None;
                        let decode_indices_ =
                            flutter_rust_bridge::for_generated::lockable_compute_decode_order(
                                vec![flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                    &api_that, 0, false,
                                )],
                            );
                        for i in decode_indices_ {
                            match i {
                                0 => {
                                    api_that_guard =
                                        Some(api_that.lockable_decode_async_ref().await)
                                }
                                _ => unreachable!(),
                            }
                        }
                        let api_that_guard = api_that_guard.unwrap();
                        let output_ok =
                            crate::api::bind::type_bind::MusicListW::get_music_aggregators(
                                &*api_that_guard,
                                api_page,
                                api_limit,
                            )
                            .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicListW_get_musiclist_info_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicListW_get_musiclist_info",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicListW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::api::bind::type_bind::MusicListW::get_musiclist_info(&*api_that_guard),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicListW_source_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicListW_source",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicListW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::api::bind::type_bind::MusicListW::source(&*api_that_guard),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicListW_to_string_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicListW_to_string",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicListW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::api::bind::type_bind::MusicListW::to_string(&*api_that_guard),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicW_fetch_album_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicW_fetch_album",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicW>,
            >>::sse_decode(&mut deserializer);
            let api_page = <u32>::sse_decode(&mut deserializer);
            let api_limit = <u32>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let mut api_that_guard = None;
                        let decode_indices_ =
                            flutter_rust_bridge::for_generated::lockable_compute_decode_order(
                                vec![flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                    &api_that, 0, false,
                                )],
                            );
                        for i in decode_indices_ {
                            match i {
                                0 => {
                                    api_that_guard =
                                        Some(api_that.lockable_decode_async_ref().await)
                                }
                                _ => unreachable!(),
                            }
                        }
                        let api_that_guard = api_that_guard.unwrap();
                        let output_ok = crate::api::bind::type_bind::MusicW::fetch_album(
                            &*api_that_guard,
                            api_page,
                            api_limit,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicW_fetch_lyric_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicW_fetch_lyric",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let mut api_that_guard = None;
                        let decode_indices_ =
                            flutter_rust_bridge::for_generated::lockable_compute_decode_order(
                                vec![flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                                    &api_that, 0, false,
                                )],
                            );
                        for i in decode_indices_ {
                            match i {
                                0 => {
                                    api_that_guard =
                                        Some(api_that.lockable_decode_async_ref().await)
                                }
                                _ => unreachable!(),
                            }
                        }
                        let api_that_guard = api_that_guard.unwrap();
                        let output_ok =
                            crate::api::bind::type_bind::MusicW::fetch_lyric(&*api_that_guard)
                                .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicW_get_extra_info_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicW_get_extra_info",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicW>,
            >>::sse_decode(&mut deserializer);
            let api_quality = <music_api::Quality>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok =
                    Result::<_, ()>::Ok(crate::api::bind::type_bind::MusicW::get_extra_info(
                        &*api_that_guard,
                        &api_quality,
                    ))?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicW_get_music_info_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicW_get_music_info",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::api::bind::type_bind::MusicW::get_music_info(&*api_that_guard),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicW_get_primary_kv_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicW_get_primary_kv",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::api::bind::type_bind::MusicW::get_primary_kv(&*api_that_guard),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicW_source_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicW_source",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(crate::api::bind::type_bind::MusicW::source(
                    &*api_that_guard,
                ))?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__bind__type_bind__MusicW_to_string_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "MusicW_to_string",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <RustOpaqueMoi<
                flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicW>,
            >>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let mut api_that_guard = None;
                let decode_indices_ =
                    flutter_rust_bridge::for_generated::lockable_compute_decode_order(vec![
                        flutter_rust_bridge::for_generated::LockableOrderInfo::new(
                            &api_that, 0, false,
                        ),
                    ]);
                for i in decode_indices_ {
                    match i {
                        0 => api_that_guard = Some(api_that.lockable_decode_sync_ref()),
                        _ => unreachable!(),
                    }
                }
                let api_that_guard = api_that_guard.unwrap();
                let output_ok = Result::<_, ()>::Ok(
                    crate::api::bind::type_bind::MusicW::to_string(&*api_that_guard),
                )?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__cache__file_cache__cache_file_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "cache_file",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_file = <String>::sse_decode(&mut deserializer);
            let api_cache_path = <String>::sse_decode(&mut deserializer);
            let api_filename = <Option<String>>::sse_decode(&mut deserializer);
            let api_export_root = <Option<String>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok = crate::api::cache::file_cache::cache_file(
                            &api_file,
                            &api_cache_path,
                            api_filename,
                            api_export_root,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__cache__file_cache__delete_cache_file_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "delete_cache_file",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_file = <String>::sse_decode(&mut deserializer);
            let api_cache_path = <String>::sse_decode(&mut deserializer);
            let api_filename = <Option<String>>::sse_decode(&mut deserializer);
            let api_export_root = <Option<String>>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok = crate::api::cache::file_cache::delete_cache_file(
                            &api_file,
                            &api_cache_path,
                            api_filename,
                            api_export_root,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__cache__file_cache__gen_hash_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "gen_hash",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_str_ = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let output_ok =
                        Result::<_, ()>::Ok(crate::api::cache::file_cache::gen_hash(&api_str_))?;
                    Ok(output_ok)
                })())
            }
        },
    )
}
fn wire__crate__api__cache__file_cache__use_cache_file_impl(
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_sync::<flutter_rust_bridge::for_generated::SseCodec, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "use_cache_file",
            port: None,
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Sync,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_file = <String>::sse_decode(&mut deserializer);
            let api_cache_path = <String>::sse_decode(&mut deserializer);
            let api_filename = <Option<String>>::sse_decode(&mut deserializer);
            let api_export_root = <Option<String>>::sse_decode(&mut deserializer);
            deserializer.end();
            transform_result_sse::<_, ()>((move || {
                let output_ok =
                    Result::<_, ()>::Ok(crate::api::cache::file_cache::use_cache_file(
                        &api_file,
                        &api_cache_path,
                        api_filename,
                        api_export_root,
                    ))?;
                Ok(output_ok)
            })())
        },
    )
}
fn wire__crate__api__cache__fs_util__copy_directory_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "copy_directory",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_src = <String>::sse_decode(&mut deserializer);
            let api_dst = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::cache::fs_util::copy_directory(&api_src, &api_dst).await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__cache__fs_util__copy_file_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "copy_file",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_from = <String>::sse_decode(&mut deserializer);
            let api_to = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::cache::fs_util::copy_file(&api_from, &api_to).await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__cache__fs_util__remove_dir_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "remove_dir",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_dir = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok = crate::api::cache::fs_util::remove_dir(&api_dir).await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__cache__fs_util__remove_file_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "remove_file",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_file = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok = crate::api::cache::fs_util::remove_file(&api_file).await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__cache__fs_util__rename_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "rename",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_from = <String>::sse_decode(&mut deserializer);
            let api_to = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::cache::fs_util::rename(&api_from, &api_to).await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__cache__music_cache__cache_music_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "cache_music",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_music_info =
                <music_api::MusicInfo>::sse_decode(&mut deserializer);
            let api_playinfo =
                <crate::api::types::playinfo::PlayInfo>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok = crate::api::cache::music_cache::cache_music(
                            &api_music_info,
                            &api_playinfo,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__cache__music_cache__delete_music_cache_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "delete_music_cache",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_music_info =
                <music_api::MusicInfo>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::cache::music_cache::delete_music_cache(&api_music_info)
                                .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__cache__music_cache__get_cache_playinfo_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "get_cache_playinfo",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_music_info =
                <music_api::MusicInfo>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::cache::music_cache::get_cache_playinfo(&api_music_info)
                                .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__cache__music_cache__has_cache_playinfo_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "has_cache_playinfo",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_music_info =
                <music_api::MusicInfo>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::cache::music_cache::has_cache_playinfo(&api_music_info)
                                .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__init__init_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "init",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, ()>(
                    (move || async move {
                        let output_ok = Result::<_, ()>::Ok({
                            crate::api::init::init().await;
                        })?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__init__init_backend_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "init_backend",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_store_root = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok = crate::api::init::init_backend(api_store_root).await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__types__config__config_load_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "config_load",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok = crate::api::types::config::Config::load().await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__types__config__config_save_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "config_save",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that = <crate::api::types::config::Config>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok = crate::api::types::config::Config::save(&api_that).await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__types__extern_api__extern_api_fetch_update_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "extern_api_fetch_update",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_that =
                <crate::api::types::extern_api::ExternApi>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::types::extern_api::ExternApi::fetch_update(api_that)
                                .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__types__extern_api__extern_api_from_path_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "extern_api_from_path",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_path = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::types::extern_api::ExternApi::from_path(&api_path).await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__types__extern_api__extern_api_from_url_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "extern_api_from_url",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_url = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::types::extern_api::ExternApi::from_url(&api_url).await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__types__version__check_update_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "check_update",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_current_version = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::types::version::check_update(&api_current_version).await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__types__version__get_release_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "get_release",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok = crate::api::types::version::get_release().await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__utils__crypto__rc4_decrypt_from_base64_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "rc4_decrypt_from_base64",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_key = <String>::sse_decode(&mut deserializer);
            let api_input = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok = crate::api::utils::crypto::rc4_decrypt_from_base64(
                            &api_key, &api_input,
                        )
                        .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__utils__crypto__rc4_encrypt_to_base64_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "rc4_encrypt_to_base64",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_key = <String>::sse_decode(&mut deserializer);
            let api_input = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, flutter_rust_bridge::for_generated::anyhow::Error>(
                    (move || async move {
                        let output_ok =
                            crate::api::utils::crypto::rc4_encrypt_to_base64(&api_key, &api_input)
                                .await?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__utils__http_helper__send_request_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_async::<flutter_rust_bridge::for_generated::SseCodec, _, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "send_request",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_method = <String>::sse_decode(&mut deserializer);
            let api_headers =
                <std::collections::HashMap<String, String>>::sse_decode(&mut deserializer);
            let api_url = <String>::sse_decode(&mut deserializer);
            let api_payload = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| async move {
                transform_result_sse::<_, ()>(
                    (move || async move {
                        let output_ok = Result::<_, ()>::Ok(
                            crate::api::utils::http_helper::send_request(
                                &api_method,
                                api_headers,
                                &api_url,
                                &api_payload,
                            )
                            .await,
                        )?;
                        Ok(output_ok)
                    })()
                    .await,
                )
            }
        },
    )
}
fn wire__crate__api__utils__path_util__url_encode_special_chars_impl(
    port_: flutter_rust_bridge::for_generated::MessagePort,
    ptr_: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len_: i32,
    data_len_: i32,
) {
    FLUTTER_RUST_BRIDGE_HANDLER.wrap_normal::<flutter_rust_bridge::for_generated::SseCodec, _, _>(
        flutter_rust_bridge::for_generated::TaskInfo {
            debug_name: "url_encode_special_chars",
            port: Some(port_),
            mode: flutter_rust_bridge::for_generated::FfiCallMode::Normal,
        },
        move || {
            let message = unsafe {
                flutter_rust_bridge::for_generated::Dart2RustMessageSse::from_wire(
                    ptr_,
                    rust_vec_len_,
                    data_len_,
                )
            };
            let mut deserializer =
                flutter_rust_bridge::for_generated::SseDeserializer::new(message);
            let api_input = <String>::sse_decode(&mut deserializer);
            deserializer.end();
            move |context| {
                transform_result_sse::<_, ()>((move || {
                    let output_ok = Result::<_, ()>::Ok(
                        crate::api::utils::path_util::url_encode_special_chars(&api_input),
                    )?;
                    Ok(output_ok)
                })())
            }
        },
    )
}

// Section: static_checks

#[allow(clippy::unnecessary_literal_unwrap)]
const _: fn() = || {
    {
        let ExtraInfo = None::<music_api::ExtraInfo>.unwrap();
        let _: Option<u32> = ExtraInfo.play_count;
        let _: Option<u32> = ExtraInfo.music_count;
    }
    {
        let MusicFuzzFilter = None::<music_api::MusicFuzzFilter>.unwrap();
        let _: Option<String> = MusicFuzzFilter.name;
        let _: Vec<String> = MusicFuzzFilter.artist;
        let _: Option<String> = MusicFuzzFilter.album;
    }
    {
        let MusicInfo = None::<music_api::MusicInfo>.unwrap();
        let _: i64 = MusicInfo.id;
        let _: String = MusicInfo.source;
        let _: String = MusicInfo.name;
        let _: Vec<String> = MusicInfo.artist;
        let _: Option<u32> = MusicInfo.duration;
        let _: Option<String> = MusicInfo.album;
        let _: Vec<music_api::Quality> = MusicInfo.qualities;
        let _: Option<music_api::Quality> = MusicInfo.default_quality;
        let _: Option<String> = MusicInfo.art_pic;
        let _: Option<String> = MusicInfo.lyric;
    }
    {
        let MusicListInfo = None::<music_api::MusicListInfo>.unwrap();
        let _: i64 = MusicListInfo.id;
        let _: String = MusicListInfo.name;
        let _: String = MusicListInfo.art_pic;
        let _: String = MusicListInfo.desc;
        let _: Option<music_api::ExtraInfo> = MusicListInfo.extra;
    }
    {
        let Quality = None::<music_api::Quality>.unwrap();
        let _: String = Quality.short;
        let _: Option<String> = Quality.level;
        let _: Option<u32> = Quality.bitrate;
        let _: Option<String> = Quality.format;
        let _: Option<String> = Quality.size;
    }
};

// Section: related_funcs

flutter_rust_bridge::frb_generated_moi_arc_impl_value!(
    flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>
);
flutter_rust_bridge::frb_generated_moi_arc_impl_value!(
    flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicListW>
);
flutter_rust_bridge::frb_generated_moi_arc_impl_value!(
    flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicW>
);

// Section: dart2rust

impl SseDecode for flutter_rust_bridge::for_generated::anyhow::Error {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <String>::sse_decode(deserializer);
        return flutter_rust_bridge::for_generated::anyhow::anyhow!("{}", inner);
    }
}

impl SseDecode for MusicAggregatorW {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <RustOpaqueMoi<
            flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>,
        >>::sse_decode(deserializer);
        return flutter_rust_bridge::for_generated::rust_auto_opaque_decode_owned(inner);
    }
}

impl SseDecode for MusicListW {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <RustOpaqueMoi<
            flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicListW>,
        >>::sse_decode(deserializer);
        return flutter_rust_bridge::for_generated::rust_auto_opaque_decode_owned(inner);
    }
}

impl SseDecode for MusicW {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <RustOpaqueMoi<
            flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicW>,
        >>::sse_decode(deserializer);
        return flutter_rust_bridge::for_generated::rust_auto_opaque_decode_owned(inner);
    }
}

impl SseDecode for chrono::DateTime<chrono::Utc> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <i64>::sse_decode(deserializer);
        return chrono::DateTime::<chrono::Utc>::from_naive_utc_and_offset(
            chrono::DateTime::from_timestamp_micros(inner)
                .expect("invalid or out-of-range datetime")
                .naive_utc(),
            chrono::Utc,
        );
    }
}

impl SseDecode for std::collections::HashMap<String, String> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <Vec<(String, String)>>::sse_decode(deserializer);
        return inner.into_iter().collect();
    }
}

impl SseDecode
    for RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>>
{
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <usize>::sse_decode(deserializer);
        return decode_rust_opaque_moi(inner);
    }
}

impl SseDecode
    for RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicListW>>
{
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <usize>::sse_decode(deserializer);
        return decode_rust_opaque_moi(inner);
    }
}

impl SseDecode for RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicW>> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <usize>::sse_decode(deserializer);
        return decode_rust_opaque_moi(inner);
    }
}

impl SseDecode for String {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut inner = <Vec<u8>>::sse_decode(deserializer);
        return String::from_utf8(inner).unwrap();
    }
}

impl SseDecode for crate::api::bind::factory_bind::AggregatorOnlineFactoryW {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        return crate::api::bind::factory_bind::AggregatorOnlineFactoryW();
    }
}

impl SseDecode for crate::api::types::version::Asset {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_url = <String>::sse_decode(deserializer);
        let mut var_id = <u64>::sse_decode(deserializer);
        let mut var_nodeId = <String>::sse_decode(deserializer);
        let mut var_name = <String>::sse_decode(deserializer);
        let mut var_label = <Option<String>>::sse_decode(deserializer);
        let mut var_uploader = <crate::api::types::version::Author>::sse_decode(deserializer);
        let mut var_contentType = <String>::sse_decode(deserializer);
        let mut var_state = <String>::sse_decode(deserializer);
        let mut var_size = <u64>::sse_decode(deserializer);
        let mut var_downloadCount = <u64>::sse_decode(deserializer);
        let mut var_createdAt = <String>::sse_decode(deserializer);
        let mut var_updatedAt = <String>::sse_decode(deserializer);
        let mut var_browserDownloadUrl = <String>::sse_decode(deserializer);
        return crate::api::types::version::Asset {
            url: var_url,
            id: var_id,
            node_id: var_nodeId,
            name: var_name,
            label: var_label,
            uploader: var_uploader,
            content_type: var_contentType,
            state: var_state,
            size: var_size,
            download_count: var_downloadCount,
            created_at: var_createdAt,
            updated_at: var_updatedAt,
            browser_download_url: var_browserDownloadUrl,
        };
    }
}

impl SseDecode for crate::api::types::version::Author {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_login = <String>::sse_decode(deserializer);
        let mut var_id = <u64>::sse_decode(deserializer);
        let mut var_nodeId = <String>::sse_decode(deserializer);
        let mut var_avatarUrl = <String>::sse_decode(deserializer);
        let mut var_gravatarId = <String>::sse_decode(deserializer);
        let mut var_url = <String>::sse_decode(deserializer);
        let mut var_htmlUrl = <String>::sse_decode(deserializer);
        let mut var_followersUrl = <String>::sse_decode(deserializer);
        let mut var_followingUrl = <String>::sse_decode(deserializer);
        let mut var_gistsUrl = <String>::sse_decode(deserializer);
        let mut var_starredUrl = <String>::sse_decode(deserializer);
        let mut var_subscriptionsUrl = <String>::sse_decode(deserializer);
        let mut var_organizationsUrl = <String>::sse_decode(deserializer);
        let mut var_reposUrl = <String>::sse_decode(deserializer);
        let mut var_eventsUrl = <String>::sse_decode(deserializer);
        let mut var_receivedEventsUrl = <String>::sse_decode(deserializer);
        let mut var_type = <String>::sse_decode(deserializer);
        let mut var_siteAdmin = <bool>::sse_decode(deserializer);
        return crate::api::types::version::Author {
            login: var_login,
            id: var_id,
            node_id: var_nodeId,
            avatar_url: var_avatarUrl,
            gravatar_id: var_gravatarId,
            url: var_url,
            html_url: var_htmlUrl,
            followers_url: var_followersUrl,
            following_url: var_followingUrl,
            gists_url: var_gistsUrl,
            starred_url: var_starredUrl,
            subscriptions_url: var_subscriptionsUrl,
            organizations_url: var_organizationsUrl,
            repos_url: var_reposUrl,
            events_url: var_eventsUrl,
            received_events_url: var_receivedEventsUrl,
            r#type: var_type,
            site_admin: var_siteAdmin,
        };
    }
}

impl SseDecode for bool {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_u8().unwrap() != 0
    }
}

impl SseDecode for crate::api::types::config::Config {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_userAgreement = <bool>::sse_decode(deserializer);
        let mut var_externApi =
            <Option<crate::api::types::extern_api::ExternApi>>::sse_decode(deserializer);
        let mut var_versionAutoUpdate = <bool>::sse_decode(deserializer);
        let mut var_externApiAutoUpdate = <bool>::sse_decode(deserializer);
        let mut var_wifiAutoQuality = <String>::sse_decode(deserializer);
        let mut var_mobileAutoQuality = <String>::sse_decode(deserializer);
        let mut var_savePicWhenAddMusicList = <bool>::sse_decode(deserializer);
        let mut var_saveLyricWhenAddMusicList = <bool>::sse_decode(deserializer);
        let mut var_exportCacheRoot = <Option<String>>::sse_decode(deserializer);
        let mut var_lastExportCacheRoot = <Option<String>>::sse_decode(deserializer);
        let mut var_externApiPath = <Option<String>>::sse_decode(deserializer);
        return crate::api::types::config::Config {
            user_agreement: var_userAgreement,
            extern_api: var_externApi,
            version_auto_update: var_versionAutoUpdate,
            extern_api_auto_update: var_externApiAutoUpdate,
            wifi_auto_quality: var_wifiAutoQuality,
            mobile_auto_quality: var_mobileAutoQuality,
            save_pic_when_add_music_list: var_savePicWhenAddMusicList,
            save_lyric_when_add_music_list: var_saveLyricWhenAddMusicList,
            export_cache_root: var_exportCacheRoot,
            last_export_cache_root: var_lastExportCacheRoot,
            extern_api_path: var_externApiPath,
        };
    }
}

impl SseDecode for crate::api::types::extern_api::ExternApi {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_url = <Option<String>>::sse_decode(deserializer);
        let mut var_localPath = <String>::sse_decode(deserializer);
        let mut var_lastHash = <Option<String>>::sse_decode(deserializer);
        let mut var_lastModifiedTime =
            <Option<chrono::DateTime<chrono::Utc>>>::sse_decode(deserializer);
        return crate::api::types::extern_api::ExternApi {
            url: var_url,
            local_path: var_localPath,
            last_hash: var_lastHash,
            last_modified_time: var_lastModifiedTime,
        };
    }
}

impl SseDecode for music_api::ExtraInfo {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_playCount = <Option<u32>>::sse_decode(deserializer);
        let mut var_musicCount = <Option<u32>>::sse_decode(deserializer);
        return music_api::ExtraInfo {
            play_count: var_playCount,
            music_count: var_musicCount,
        };
    }
}

impl SseDecode for i64 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_i64::<NativeEndian>().unwrap()
    }
}

impl SseDecode for Vec<MusicAggregatorW> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<MusicAggregatorW>::sse_decode(deserializer));
        }
        return ans_;
    }
}

impl SseDecode for Vec<MusicListW> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<MusicListW>::sse_decode(deserializer));
        }
        return ans_;
    }
}

impl SseDecode for Vec<MusicW> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<MusicW>::sse_decode(deserializer));
        }
        return ans_;
    }
}

impl SseDecode for Vec<String> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<String>::sse_decode(deserializer));
        }
        return ans_;
    }
}

impl SseDecode for Vec<crate::api::types::version::Asset> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<crate::api::types::version::Asset>::sse_decode(
                deserializer,
            ));
        }
        return ans_;
    }
}

impl SseDecode for Vec<music_api::MusicInfo> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<music_api::MusicInfo>::sse_decode(
                deserializer,
            ));
        }
        return ans_;
    }
}

impl SseDecode for Vec<music_api::MusicListInfo> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<music_api::MusicListInfo>::sse_decode(
                deserializer,
            ));
        }
        return ans_;
    }
}

impl SseDecode for Vec<i64> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<i64>::sse_decode(deserializer));
        }
        return ans_;
    }
}

impl SseDecode for Vec<u8> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<u8>::sse_decode(deserializer));
        }
        return ans_;
    }
}

impl SseDecode for Vec<music_api::Quality> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<music_api::Quality>::sse_decode(
                deserializer,
            ));
        }
        return ans_;
    }
}

impl SseDecode for Vec<(String, String)> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut len_ = <i32>::sse_decode(deserializer);
        let mut ans_ = vec![];
        for idx_ in 0..len_ {
            ans_.push(<(String, String)>::sse_decode(deserializer));
        }
        return ans_;
    }
}

impl SseDecode for music_api::MusicFuzzFilter {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_name = <Option<String>>::sse_decode(deserializer);
        let mut var_artist = <Vec<String>>::sse_decode(deserializer);
        let mut var_album = <Option<String>>::sse_decode(deserializer);
        return music_api::MusicFuzzFilter {
            name: var_name,
            artist: var_artist,
            album: var_album,
        };
    }
}

impl SseDecode for music_api::MusicInfo {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_id = <i64>::sse_decode(deserializer);
        let mut var_source = <String>::sse_decode(deserializer);
        let mut var_name = <String>::sse_decode(deserializer);
        let mut var_artist = <Vec<String>>::sse_decode(deserializer);
        let mut var_duration = <Option<u32>>::sse_decode(deserializer);
        let mut var_album = <Option<String>>::sse_decode(deserializer);
        let mut var_qualities = <Vec<music_api::Quality>>::sse_decode(deserializer);
        let mut var_defaultQuality =
            <Option<music_api::Quality>>::sse_decode(deserializer);
        let mut var_artPic = <Option<String>>::sse_decode(deserializer);
        let mut var_lyric = <Option<String>>::sse_decode(deserializer);
        return music_api::MusicInfo {
            id: var_id,
            source: var_source,
            name: var_name,
            artist: var_artist,
            duration: var_duration,
            album: var_album,
            qualities: var_qualities,
            default_quality: var_defaultQuality,
            art_pic: var_artPic,
            lyric: var_lyric,
        };
    }
}

impl SseDecode for music_api::MusicListInfo {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_id = <i64>::sse_decode(deserializer);
        let mut var_name = <String>::sse_decode(deserializer);
        let mut var_artPic = <String>::sse_decode(deserializer);
        let mut var_desc = <String>::sse_decode(deserializer);
        let mut var_extra =
            <Option<music_api::ExtraInfo>>::sse_decode(deserializer);
        return music_api::MusicListInfo {
            id: var_id,
            name: var_name,
            art_pic: var_artPic,
            desc: var_desc,
            extra: var_extra,
        };
    }
}

impl SseDecode for crate::api::bind::factory_bind::OnlineFactoryW {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        return crate::api::bind::factory_bind::OnlineFactoryW();
    }
}

impl SseDecode for Option<String> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        if (<bool>::sse_decode(deserializer)) {
            return Some(<String>::sse_decode(deserializer));
        } else {
            return None;
        }
    }
}

impl SseDecode for Option<MusicW> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        if (<bool>::sse_decode(deserializer)) {
            return Some(<MusicW>::sse_decode(deserializer));
        } else {
            return None;
        }
    }
}

impl SseDecode for Option<chrono::DateTime<chrono::Utc>> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        if (<bool>::sse_decode(deserializer)) {
            return Some(<chrono::DateTime<chrono::Utc>>::sse_decode(deserializer));
        } else {
            return None;
        }
    }
}

impl SseDecode for Option<crate::api::types::extern_api::ExternApi> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        if (<bool>::sse_decode(deserializer)) {
            return Some(<crate::api::types::extern_api::ExternApi>::sse_decode(
                deserializer,
            ));
        } else {
            return None;
        }
    }
}

impl SseDecode for Option<music_api::ExtraInfo> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        if (<bool>::sse_decode(deserializer)) {
            return Some(<music_api::ExtraInfo>::sse_decode(
                deserializer,
            ));
        } else {
            return None;
        }
    }
}

impl SseDecode for Option<music_api::MusicFuzzFilter> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        if (<bool>::sse_decode(deserializer)) {
            return Some(<music_api::MusicFuzzFilter>::sse_decode(
                deserializer,
            ));
        } else {
            return None;
        }
    }
}

impl SseDecode for Option<music_api::Quality> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        if (<bool>::sse_decode(deserializer)) {
            return Some(<music_api::Quality>::sse_decode(
                deserializer,
            ));
        } else {
            return None;
        }
    }
}

impl SseDecode for Option<crate::api::types::version::Release> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        if (<bool>::sse_decode(deserializer)) {
            return Some(<crate::api::types::version::Release>::sse_decode(
                deserializer,
            ));
        } else {
            return None;
        }
    }
}

impl SseDecode for Option<u32> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        if (<bool>::sse_decode(deserializer)) {
            return Some(<u32>::sse_decode(deserializer));
        } else {
            return None;
        }
    }
}

impl SseDecode for crate::api::types::playinfo::PlayInfo {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_uri = <String>::sse_decode(deserializer);
        let mut var_quality = <music_api::Quality>::sse_decode(deserializer);
        return crate::api::types::playinfo::PlayInfo {
            uri: var_uri,
            quality: var_quality,
        };
    }
}

impl SseDecode for music_api::Quality {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_short = <String>::sse_decode(deserializer);
        let mut var_level = <Option<String>>::sse_decode(deserializer);
        let mut var_bitrate = <Option<u32>>::sse_decode(deserializer);
        let mut var_format = <Option<String>>::sse_decode(deserializer);
        let mut var_size = <Option<String>>::sse_decode(deserializer);
        return music_api::Quality {
            short: var_short,
            level: var_level,
            bitrate: var_bitrate,
            format: var_format,
            size: var_size,
        };
    }
}

impl SseDecode for (MusicListW, Vec<MusicAggregatorW>) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_field0 = <MusicListW>::sse_decode(deserializer);
        let mut var_field1 = <Vec<MusicAggregatorW>>::sse_decode(deserializer);
        return (var_field0, var_field1);
    }
}

impl SseDecode for (String, String) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_field0 = <String>::sse_decode(deserializer);
        let mut var_field1 = <String>::sse_decode(deserializer);
        return (var_field0, var_field1);
    }
}

impl SseDecode for crate::api::types::version::Release {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        let mut var_url = <String>::sse_decode(deserializer);
        let mut var_assetsUrl = <String>::sse_decode(deserializer);
        let mut var_uploadUrl = <String>::sse_decode(deserializer);
        let mut var_htmlUrl = <String>::sse_decode(deserializer);
        let mut var_id = <u64>::sse_decode(deserializer);
        let mut var_author = <crate::api::types::version::Author>::sse_decode(deserializer);
        let mut var_nodeId = <String>::sse_decode(deserializer);
        let mut var_tagName = <String>::sse_decode(deserializer);
        let mut var_targetCommitish = <String>::sse_decode(deserializer);
        let mut var_name = <String>::sse_decode(deserializer);
        let mut var_draft = <bool>::sse_decode(deserializer);
        let mut var_prerelease = <bool>::sse_decode(deserializer);
        let mut var_createdAt = <String>::sse_decode(deserializer);
        let mut var_publishedAt = <String>::sse_decode(deserializer);
        let mut var_assets = <Vec<crate::api::types::version::Asset>>::sse_decode(deserializer);
        let mut var_tarballUrl = <String>::sse_decode(deserializer);
        let mut var_zipballUrl = <String>::sse_decode(deserializer);
        let mut var_body = <String>::sse_decode(deserializer);
        return crate::api::types::version::Release {
            url: var_url,
            assets_url: var_assetsUrl,
            upload_url: var_uploadUrl,
            html_url: var_htmlUrl,
            id: var_id,
            author: var_author,
            node_id: var_nodeId,
            tag_name: var_tagName,
            target_commitish: var_targetCommitish,
            name: var_name,
            draft: var_draft,
            prerelease: var_prerelease,
            created_at: var_createdAt,
            published_at: var_publishedAt,
            assets: var_assets,
            tarball_url: var_tarballUrl,
            zipball_url: var_zipballUrl,
            body: var_body,
        };
    }
}

impl SseDecode for crate::api::bind::factory_bind::SqlFactoryW {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        return crate::api::bind::factory_bind::SqlFactoryW();
    }
}

impl SseDecode for u32 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_u32::<NativeEndian>().unwrap()
    }
}

impl SseDecode for u64 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_u64::<NativeEndian>().unwrap()
    }
}

impl SseDecode for u8 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_u8().unwrap()
    }
}

impl SseDecode for () {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {}
}

impl SseDecode for usize {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_u64::<NativeEndian>().unwrap() as _
    }
}

impl SseDecode for i32 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_decode(deserializer: &mut flutter_rust_bridge::for_generated::SseDeserializer) -> Self {
        deserializer.cursor.read_i32::<NativeEndian>().unwrap()
    }
}

fn pde_ffi_dispatcher_primary_impl(
    func_id: i32,
    port: flutter_rust_bridge::for_generated::MessagePort,
    ptr: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len: i32,
    data_len: i32,
) {
    // Codec=Pde (Serialization + dispatch), see doc to use other codecs
    match func_id {
                        1 => wire__crate__api__bind__factory_bind__aggregator_online_factory_w_search_music_aggregator_impl(port, ptr, rust_vec_len, data_len),
2 => wire__crate__api__bind__factory_bind__online_factory_w_get_musiclist_from_share_impl(port, ptr, rust_vec_len, data_len),
3 => wire__crate__api__bind__factory_bind__online_factory_w_search_musiclist_impl(port, ptr, rust_vec_len, data_len),
4 => wire__crate__api__bind__factory_bind__sql_factory_w_add_musics_impl(port, ptr, rust_vec_len, data_len),
5 => wire__crate__api__bind__factory_bind__sql_factory_w_change_music_default_source_impl(port, ptr, rust_vec_len, data_len),
6 => wire__crate__api__bind__factory_bind__sql_factory_w_change_music_info_impl(port, ptr, rust_vec_len, data_len),
7 => wire__crate__api__bind__factory_bind__sql_factory_w_change_musiclist_info_impl(port, ptr, rust_vec_len, data_len),
8 => wire__crate__api__bind__factory_bind__sql_factory_w_clean_unused_music_data_impl(port, ptr, rust_vec_len, data_len),
9 => wire__crate__api__bind__factory_bind__sql_factory_w_clean_unused_musiclist_impl(port, ptr, rust_vec_len, data_len),
10 => wire__crate__api__bind__factory_bind__sql_factory_w_create_musiclist_impl(port, ptr, rust_vec_len, data_len),
11 => wire__crate__api__bind__factory_bind__sql_factory_w_del_duplicate_musics_of_musiclist_impl(port, ptr, rust_vec_len, data_len),
12 => wire__crate__api__bind__factory_bind__sql_factory_w_del_musiclist_impl(port, ptr, rust_vec_len, data_len),
13 => wire__crate__api__bind__factory_bind__sql_factory_w_del_musics_impl(port, ptr, rust_vec_len, data_len),
14 => wire__crate__api__bind__factory_bind__sql_factory_w_get_all_musiclists_impl(port, ptr, rust_vec_len, data_len),
15 => wire__crate__api__bind__factory_bind__sql_factory_w_get_all_musics_impl(port, ptr, rust_vec_len, data_len),
16 => wire__crate__api__bind__factory_bind__sql_factory_w_get_music_by_id_impl(port, ptr, rust_vec_len, data_len),
17 => wire__crate__api__bind__factory_bind__sql_factory_w_init_from_path_impl(port, ptr, rust_vec_len, data_len),
18 => wire__crate__api__bind__factory_bind__sql_factory_w_read_music_data_impl(port, ptr, rust_vec_len, data_len),
19 => wire__crate__api__bind__factory_bind__sql_factory_w_reorder_musiclist_impl(port, ptr, rust_vec_len, data_len),
20 => wire__crate__api__bind__factory_bind__sql_factory_w_reorder_musics_impl(port, ptr, rust_vec_len, data_len),
21 => wire__crate__api__bind__factory_bind__sql_factory_w_replace_musics_impl(port, ptr, rust_vec_len, data_len),
22 => wire__crate__api__bind__factory_bind__sql_factory_w_shutdown_impl(port, ptr, rust_vec_len, data_len),
23 => wire__crate__api__bind__type_bind__MusicAggregatorW_add_music_impl(port, ptr, rust_vec_len, data_len),
26 => wire__crate__api__bind__type_bind__MusicAggregatorW_fetch_album_impl(port, ptr, rust_vec_len, data_len),
27 => wire__crate__api__bind__type_bind__MusicAggregatorW_fetch_lyric_impl(port, ptr, rust_vec_len, data_len),
28 => wire__crate__api__bind__type_bind__MusicAggregatorW_fetch_musics_impl(port, ptr, rust_vec_len, data_len),
34 => wire__crate__api__bind__type_bind__MusicAggregatorW_get_music_impl(port, ptr, rust_vec_len, data_len),
37 => wire__crate__api__bind__type_bind__MusicAggregatorW_set_default_source_impl(port, ptr, rust_vec_len, data_len),
39 => wire__crate__api__bind__type_bind__MusicListW_fetch_all_music_aggregators_impl(port, ptr, rust_vec_len, data_len),
40 => wire__crate__api__bind__type_bind__MusicListW_get_music_aggregators_impl(port, ptr, rust_vec_len, data_len),
44 => wire__crate__api__bind__type_bind__MusicW_fetch_album_impl(port, ptr, rust_vec_len, data_len),
45 => wire__crate__api__bind__type_bind__MusicW_fetch_lyric_impl(port, ptr, rust_vec_len, data_len),
51 => wire__crate__api__cache__file_cache__cache_file_impl(port, ptr, rust_vec_len, data_len),
52 => wire__crate__api__cache__file_cache__delete_cache_file_impl(port, ptr, rust_vec_len, data_len),
53 => wire__crate__api__cache__file_cache__gen_hash_impl(port, ptr, rust_vec_len, data_len),
55 => wire__crate__api__cache__fs_util__copy_directory_impl(port, ptr, rust_vec_len, data_len),
56 => wire__crate__api__cache__fs_util__copy_file_impl(port, ptr, rust_vec_len, data_len),
57 => wire__crate__api__cache__fs_util__remove_dir_impl(port, ptr, rust_vec_len, data_len),
58 => wire__crate__api__cache__fs_util__remove_file_impl(port, ptr, rust_vec_len, data_len),
59 => wire__crate__api__cache__fs_util__rename_impl(port, ptr, rust_vec_len, data_len),
60 => wire__crate__api__cache__music_cache__cache_music_impl(port, ptr, rust_vec_len, data_len),
61 => wire__crate__api__cache__music_cache__delete_music_cache_impl(port, ptr, rust_vec_len, data_len),
62 => wire__crate__api__cache__music_cache__get_cache_playinfo_impl(port, ptr, rust_vec_len, data_len),
63 => wire__crate__api__cache__music_cache__has_cache_playinfo_impl(port, ptr, rust_vec_len, data_len),
64 => wire__crate__api__init__init_impl(port, ptr, rust_vec_len, data_len),
65 => wire__crate__api__init__init_backend_impl(port, ptr, rust_vec_len, data_len),
66 => wire__crate__api__types__config__config_load_impl(port, ptr, rust_vec_len, data_len),
67 => wire__crate__api__types__config__config_save_impl(port, ptr, rust_vec_len, data_len),
68 => wire__crate__api__types__extern_api__extern_api_fetch_update_impl(port, ptr, rust_vec_len, data_len),
69 => wire__crate__api__types__extern_api__extern_api_from_path_impl(port, ptr, rust_vec_len, data_len),
70 => wire__crate__api__types__extern_api__extern_api_from_url_impl(port, ptr, rust_vec_len, data_len),
71 => wire__crate__api__types__version__check_update_impl(port, ptr, rust_vec_len, data_len),
72 => wire__crate__api__types__version__get_release_impl(port, ptr, rust_vec_len, data_len),
73 => wire__crate__api__utils__crypto__rc4_decrypt_from_base64_impl(port, ptr, rust_vec_len, data_len),
74 => wire__crate__api__utils__crypto__rc4_encrypt_to_base64_impl(port, ptr, rust_vec_len, data_len),
75 => wire__crate__api__utils__http_helper__send_request_impl(port, ptr, rust_vec_len, data_len),
76 => wire__crate__api__utils__path_util__url_encode_special_chars_impl(port, ptr, rust_vec_len, data_len),
                        _ => unreachable!(),
                    }
}

fn pde_ffi_dispatcher_sync_impl(
    func_id: i32,
    ptr: flutter_rust_bridge::for_generated::PlatformGeneralizedUint8ListPtr,
    rust_vec_len: i32,
    data_len: i32,
) -> flutter_rust_bridge::for_generated::WireSyncRust2DartSse {
    // Codec=Pde (Serialization + dispatch), see doc to use other codecs
    match func_id {
        24 => wire__crate__api__bind__type_bind__MusicAggregatorW_belong_to_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        25 => wire__crate__api__bind__type_bind__MusicAggregatorW_clone_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        29 => wire__crate__api__bind__type_bind__MusicAggregatorW_get_all_musics_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        30 => wire__crate__api__bind__type_bind__MusicAggregatorW_get_all_musics_owned_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        31 => wire__crate__api__bind__type_bind__MusicAggregatorW_get_available_sources_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        32 => wire__crate__api__bind__type_bind__MusicAggregatorW_get_default_music_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        33 => wire__crate__api__bind__type_bind__MusicAggregatorW_get_default_source_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        35 => wire__crate__api__bind__type_bind__MusicAggregatorW_get_music_id_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        36 => wire__crate__api__bind__type_bind__MusicAggregatorW_match_filter_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        38 => wire__crate__api__bind__type_bind__MusicAggregatorW_to_string_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        41 => wire__crate__api__bind__type_bind__MusicListW_get_musiclist_info_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        42 => {
            wire__crate__api__bind__type_bind__MusicListW_source_impl(ptr, rust_vec_len, data_len)
        }
        43 => wire__crate__api__bind__type_bind__MusicListW_to_string_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        46 => wire__crate__api__bind__type_bind__MusicW_get_extra_info_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        47 => wire__crate__api__bind__type_bind__MusicW_get_music_info_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        48 => wire__crate__api__bind__type_bind__MusicW_get_primary_kv_impl(
            ptr,
            rust_vec_len,
            data_len,
        ),
        49 => wire__crate__api__bind__type_bind__MusicW_source_impl(ptr, rust_vec_len, data_len),
        50 => wire__crate__api__bind__type_bind__MusicW_to_string_impl(ptr, rust_vec_len, data_len),
        54 => wire__crate__api__cache__file_cache__use_cache_file_impl(ptr, rust_vec_len, data_len),
        _ => unreachable!(),
    }
}

// Section: rust2dart

// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for FrbWrapper<MusicAggregatorW> {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        flutter_rust_bridge::for_generated::rust_auto_opaque_encode::<_, MoiArc<_>>(self.0)
            .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive for FrbWrapper<MusicAggregatorW> {}

impl flutter_rust_bridge::IntoIntoDart<FrbWrapper<MusicAggregatorW>> for MusicAggregatorW {
    fn into_into_dart(self) -> FrbWrapper<MusicAggregatorW> {
        self.into()
    }
}

// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for FrbWrapper<MusicListW> {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        flutter_rust_bridge::for_generated::rust_auto_opaque_encode::<_, MoiArc<_>>(self.0)
            .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive for FrbWrapper<MusicListW> {}

impl flutter_rust_bridge::IntoIntoDart<FrbWrapper<MusicListW>> for MusicListW {
    fn into_into_dart(self) -> FrbWrapper<MusicListW> {
        self.into()
    }
}

// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for FrbWrapper<MusicW> {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        flutter_rust_bridge::for_generated::rust_auto_opaque_encode::<_, MoiArc<_>>(self.0)
            .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive for FrbWrapper<MusicW> {}

impl flutter_rust_bridge::IntoIntoDart<FrbWrapper<MusicW>> for MusicW {
    fn into_into_dart(self) -> FrbWrapper<MusicW> {
        self.into()
    }
}

// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::api::bind::factory_bind::AggregatorOnlineFactoryW {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        Vec::<u8>::new().into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::api::bind::factory_bind::AggregatorOnlineFactoryW
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::api::bind::factory_bind::AggregatorOnlineFactoryW>
    for crate::api::bind::factory_bind::AggregatorOnlineFactoryW
{
    fn into_into_dart(self) -> crate::api::bind::factory_bind::AggregatorOnlineFactoryW {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::api::types::version::Asset {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.url.into_into_dart().into_dart(),
            self.id.into_into_dart().into_dart(),
            self.node_id.into_into_dart().into_dart(),
            self.name.into_into_dart().into_dart(),
            self.label.into_into_dart().into_dart(),
            self.uploader.into_into_dart().into_dart(),
            self.content_type.into_into_dart().into_dart(),
            self.state.into_into_dart().into_dart(),
            self.size.into_into_dart().into_dart(),
            self.download_count.into_into_dart().into_dart(),
            self.created_at.into_into_dart().into_dart(),
            self.updated_at.into_into_dart().into_dart(),
            self.browser_download_url.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::api::types::version::Asset
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::api::types::version::Asset>
    for crate::api::types::version::Asset
{
    fn into_into_dart(self) -> crate::api::types::version::Asset {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::api::types::version::Author {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.login.into_into_dart().into_dart(),
            self.id.into_into_dart().into_dart(),
            self.node_id.into_into_dart().into_dart(),
            self.avatar_url.into_into_dart().into_dart(),
            self.gravatar_id.into_into_dart().into_dart(),
            self.url.into_into_dart().into_dart(),
            self.html_url.into_into_dart().into_dart(),
            self.followers_url.into_into_dart().into_dart(),
            self.following_url.into_into_dart().into_dart(),
            self.gists_url.into_into_dart().into_dart(),
            self.starred_url.into_into_dart().into_dart(),
            self.subscriptions_url.into_into_dart().into_dart(),
            self.organizations_url.into_into_dart().into_dart(),
            self.repos_url.into_into_dart().into_dart(),
            self.events_url.into_into_dart().into_dart(),
            self.received_events_url.into_into_dart().into_dart(),
            self.r#type.into_into_dart().into_dart(),
            self.site_admin.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::api::types::version::Author
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::api::types::version::Author>
    for crate::api::types::version::Author
{
    fn into_into_dart(self) -> crate::api::types::version::Author {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::api::types::config::Config {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.user_agreement.into_into_dart().into_dart(),
            self.extern_api.into_into_dart().into_dart(),
            self.version_auto_update.into_into_dart().into_dart(),
            self.extern_api_auto_update.into_into_dart().into_dart(),
            self.wifi_auto_quality.into_into_dart().into_dart(),
            self.mobile_auto_quality.into_into_dart().into_dart(),
            self.save_pic_when_add_music_list
                .into_into_dart()
                .into_dart(),
            self.save_lyric_when_add_music_list
                .into_into_dart()
                .into_dart(),
            self.export_cache_root.into_into_dart().into_dart(),
            self.last_export_cache_root.into_into_dart().into_dart(),
            self.extern_api_path.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::api::types::config::Config
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::api::types::config::Config>
    for crate::api::types::config::Config
{
    fn into_into_dart(self) -> crate::api::types::config::Config {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::api::types::extern_api::ExternApi {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.url.into_into_dart().into_dart(),
            self.local_path.into_into_dart().into_dart(),
            self.last_hash.into_into_dart().into_dart(),
            self.last_modified_time.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::api::types::extern_api::ExternApi
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::api::types::extern_api::ExternApi>
    for crate::api::types::extern_api::ExternApi
{
    fn into_into_dart(self) -> crate::api::types::extern_api::ExternApi {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for FrbWrapper<music_api::ExtraInfo> {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.0.play_count.into_into_dart().into_dart(),
            self.0.music_count.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for FrbWrapper<music_api::ExtraInfo>
{
}
impl flutter_rust_bridge::IntoIntoDart<FrbWrapper<music_api::ExtraInfo>>
    for music_api::ExtraInfo
{
    fn into_into_dart(self) -> FrbWrapper<music_api::ExtraInfo> {
        self.into()
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for FrbWrapper<music_api::MusicFuzzFilter> {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.0.name.into_into_dart().into_dart(),
            self.0.artist.into_into_dart().into_dart(),
            self.0.album.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for FrbWrapper<music_api::MusicFuzzFilter>
{
}
impl flutter_rust_bridge::IntoIntoDart<FrbWrapper<music_api::MusicFuzzFilter>>
    for music_api::MusicFuzzFilter
{
    fn into_into_dart(self) -> FrbWrapper<music_api::MusicFuzzFilter> {
        self.into()
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for FrbWrapper<music_api::MusicInfo> {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.0.id.into_into_dart().into_dart(),
            self.0.source.into_into_dart().into_dart(),
            self.0.name.into_into_dart().into_dart(),
            self.0.artist.into_into_dart().into_dart(),
            self.0.duration.into_into_dart().into_dart(),
            self.0.album.into_into_dart().into_dart(),
            self.0.qualities.into_into_dart().into_dart(),
            self.0.default_quality.into_into_dart().into_dart(),
            self.0.art_pic.into_into_dart().into_dart(),
            self.0.lyric.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for FrbWrapper<music_api::MusicInfo>
{
}
impl flutter_rust_bridge::IntoIntoDart<FrbWrapper<music_api::MusicInfo>>
    for music_api::MusicInfo
{
    fn into_into_dart(self) -> FrbWrapper<music_api::MusicInfo> {
        self.into()
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for FrbWrapper<music_api::MusicListInfo> {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.0.id.into_into_dart().into_dart(),
            self.0.name.into_into_dart().into_dart(),
            self.0.art_pic.into_into_dart().into_dart(),
            self.0.desc.into_into_dart().into_dart(),
            self.0.extra.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for FrbWrapper<music_api::MusicListInfo>
{
}
impl flutter_rust_bridge::IntoIntoDart<FrbWrapper<music_api::MusicListInfo>>
    for music_api::MusicListInfo
{
    fn into_into_dart(self) -> FrbWrapper<music_api::MusicListInfo> {
        self.into()
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::api::bind::factory_bind::OnlineFactoryW {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        Vec::<u8>::new().into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::api::bind::factory_bind::OnlineFactoryW
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::api::bind::factory_bind::OnlineFactoryW>
    for crate::api::bind::factory_bind::OnlineFactoryW
{
    fn into_into_dart(self) -> crate::api::bind::factory_bind::OnlineFactoryW {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::api::types::playinfo::PlayInfo {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.uri.into_into_dart().into_dart(),
            self.quality.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::api::types::playinfo::PlayInfo
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::api::types::playinfo::PlayInfo>
    for crate::api::types::playinfo::PlayInfo
{
    fn into_into_dart(self) -> crate::api::types::playinfo::PlayInfo {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for FrbWrapper<music_api::Quality> {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.0.short.into_into_dart().into_dart(),
            self.0.level.into_into_dart().into_dart(),
            self.0.bitrate.into_into_dart().into_dart(),
            self.0.format.into_into_dart().into_dart(),
            self.0.size.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for FrbWrapper<music_api::Quality>
{
}
impl flutter_rust_bridge::IntoIntoDart<FrbWrapper<music_api::Quality>>
    for music_api::Quality
{
    fn into_into_dart(self) -> FrbWrapper<music_api::Quality> {
        self.into()
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::api::types::version::Release {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        [
            self.url.into_into_dart().into_dart(),
            self.assets_url.into_into_dart().into_dart(),
            self.upload_url.into_into_dart().into_dart(),
            self.html_url.into_into_dart().into_dart(),
            self.id.into_into_dart().into_dart(),
            self.author.into_into_dart().into_dart(),
            self.node_id.into_into_dart().into_dart(),
            self.tag_name.into_into_dart().into_dart(),
            self.target_commitish.into_into_dart().into_dart(),
            self.name.into_into_dart().into_dart(),
            self.draft.into_into_dart().into_dart(),
            self.prerelease.into_into_dart().into_dart(),
            self.created_at.into_into_dart().into_dart(),
            self.published_at.into_into_dart().into_dart(),
            self.assets.into_into_dart().into_dart(),
            self.tarball_url.into_into_dart().into_dart(),
            self.zipball_url.into_into_dart().into_dart(),
            self.body.into_into_dart().into_dart(),
        ]
        .into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::api::types::version::Release
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::api::types::version::Release>
    for crate::api::types::version::Release
{
    fn into_into_dart(self) -> crate::api::types::version::Release {
        self
    }
}
// Codec=Dco (DartCObject based), see doc to use other codecs
impl flutter_rust_bridge::IntoDart for crate::api::bind::factory_bind::SqlFactoryW {
    fn into_dart(self) -> flutter_rust_bridge::for_generated::DartAbi {
        Vec::<u8>::new().into_dart()
    }
}
impl flutter_rust_bridge::for_generated::IntoDartExceptPrimitive
    for crate::api::bind::factory_bind::SqlFactoryW
{
}
impl flutter_rust_bridge::IntoIntoDart<crate::api::bind::factory_bind::SqlFactoryW>
    for crate::api::bind::factory_bind::SqlFactoryW
{
    fn into_into_dart(self) -> crate::api::bind::factory_bind::SqlFactoryW {
        self
    }
}

impl SseEncode for flutter_rust_bridge::for_generated::anyhow::Error {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <String>::sse_encode(format!("{:?}", self), serializer);
    }
}

impl SseEncode for MusicAggregatorW {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>>>::sse_encode(flutter_rust_bridge::for_generated::rust_auto_opaque_encode::<_, MoiArc<_>>(self), serializer);
    }
}

impl SseEncode for MusicListW {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicListW>>>::sse_encode(flutter_rust_bridge::for_generated::rust_auto_opaque_encode::<_, MoiArc<_>>(self), serializer);
    }
}

impl SseEncode for MusicW {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicW>>>::sse_encode(flutter_rust_bridge::for_generated::rust_auto_opaque_encode::<_, MoiArc<_>>(self), serializer);
    }
}

impl SseEncode for chrono::DateTime<chrono::Utc> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i64>::sse_encode(self.timestamp_micros(), serializer);
    }
}

impl SseEncode for std::collections::HashMap<String, String> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <Vec<(String, String)>>::sse_encode(self.into_iter().collect(), serializer);
    }
}

impl SseEncode
    for RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicAggregatorW>>
{
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        let (ptr, size) = self.sse_encode_raw();
        <usize>::sse_encode(ptr, serializer);
        <i32>::sse_encode(size, serializer);
    }
}

impl SseEncode
    for RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicListW>>
{
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        let (ptr, size) = self.sse_encode_raw();
        <usize>::sse_encode(ptr, serializer);
        <i32>::sse_encode(size, serializer);
    }
}

impl SseEncode for RustOpaqueMoi<flutter_rust_bridge::for_generated::RustAutoOpaqueInner<MusicW>> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        let (ptr, size) = self.sse_encode_raw();
        <usize>::sse_encode(ptr, serializer);
        <i32>::sse_encode(size, serializer);
    }
}

impl SseEncode for String {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <Vec<u8>>::sse_encode(self.into_bytes(), serializer);
    }
}

impl SseEncode for crate::api::bind::factory_bind::AggregatorOnlineFactoryW {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {}
}

impl SseEncode for crate::api::types::version::Asset {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <String>::sse_encode(self.url, serializer);
        <u64>::sse_encode(self.id, serializer);
        <String>::sse_encode(self.node_id, serializer);
        <String>::sse_encode(self.name, serializer);
        <Option<String>>::sse_encode(self.label, serializer);
        <crate::api::types::version::Author>::sse_encode(self.uploader, serializer);
        <String>::sse_encode(self.content_type, serializer);
        <String>::sse_encode(self.state, serializer);
        <u64>::sse_encode(self.size, serializer);
        <u64>::sse_encode(self.download_count, serializer);
        <String>::sse_encode(self.created_at, serializer);
        <String>::sse_encode(self.updated_at, serializer);
        <String>::sse_encode(self.browser_download_url, serializer);
    }
}

impl SseEncode for crate::api::types::version::Author {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <String>::sse_encode(self.login, serializer);
        <u64>::sse_encode(self.id, serializer);
        <String>::sse_encode(self.node_id, serializer);
        <String>::sse_encode(self.avatar_url, serializer);
        <String>::sse_encode(self.gravatar_id, serializer);
        <String>::sse_encode(self.url, serializer);
        <String>::sse_encode(self.html_url, serializer);
        <String>::sse_encode(self.followers_url, serializer);
        <String>::sse_encode(self.following_url, serializer);
        <String>::sse_encode(self.gists_url, serializer);
        <String>::sse_encode(self.starred_url, serializer);
        <String>::sse_encode(self.subscriptions_url, serializer);
        <String>::sse_encode(self.organizations_url, serializer);
        <String>::sse_encode(self.repos_url, serializer);
        <String>::sse_encode(self.events_url, serializer);
        <String>::sse_encode(self.received_events_url, serializer);
        <String>::sse_encode(self.r#type, serializer);
        <bool>::sse_encode(self.site_admin, serializer);
    }
}

impl SseEncode for bool {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer.cursor.write_u8(self as _).unwrap();
    }
}

impl SseEncode for crate::api::types::config::Config {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <bool>::sse_encode(self.user_agreement, serializer);
        <Option<crate::api::types::extern_api::ExternApi>>::sse_encode(self.extern_api, serializer);
        <bool>::sse_encode(self.version_auto_update, serializer);
        <bool>::sse_encode(self.extern_api_auto_update, serializer);
        <String>::sse_encode(self.wifi_auto_quality, serializer);
        <String>::sse_encode(self.mobile_auto_quality, serializer);
        <bool>::sse_encode(self.save_pic_when_add_music_list, serializer);
        <bool>::sse_encode(self.save_lyric_when_add_music_list, serializer);
        <Option<String>>::sse_encode(self.export_cache_root, serializer);
        <Option<String>>::sse_encode(self.last_export_cache_root, serializer);
        <Option<String>>::sse_encode(self.extern_api_path, serializer);
    }
}

impl SseEncode for crate::api::types::extern_api::ExternApi {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <Option<String>>::sse_encode(self.url, serializer);
        <String>::sse_encode(self.local_path, serializer);
        <Option<String>>::sse_encode(self.last_hash, serializer);
        <Option<chrono::DateTime<chrono::Utc>>>::sse_encode(self.last_modified_time, serializer);
    }
}

impl SseEncode for music_api::ExtraInfo {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <Option<u32>>::sse_encode(self.play_count, serializer);
        <Option<u32>>::sse_encode(self.music_count, serializer);
    }
}

impl SseEncode for i64 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer.cursor.write_i64::<NativeEndian>(self).unwrap();
    }
}

impl SseEncode for Vec<MusicAggregatorW> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <MusicAggregatorW>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<MusicListW> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <MusicListW>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<MusicW> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <MusicW>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<String> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <String>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<crate::api::types::version::Asset> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <crate::api::types::version::Asset>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<music_api::MusicInfo> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <music_api::MusicInfo>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<music_api::MusicListInfo> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <music_api::MusicListInfo>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<i64> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <i64>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<u8> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <u8>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<music_api::Quality> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <music_api::Quality>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for Vec<(String, String)> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i32>::sse_encode(self.len() as _, serializer);
        for item in self {
            <(String, String)>::sse_encode(item, serializer);
        }
    }
}

impl SseEncode for music_api::MusicFuzzFilter {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <Option<String>>::sse_encode(self.name, serializer);
        <Vec<String>>::sse_encode(self.artist, serializer);
        <Option<String>>::sse_encode(self.album, serializer);
    }
}

impl SseEncode for music_api::MusicInfo {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i64>::sse_encode(self.id, serializer);
        <String>::sse_encode(self.source, serializer);
        <String>::sse_encode(self.name, serializer);
        <Vec<String>>::sse_encode(self.artist, serializer);
        <Option<u32>>::sse_encode(self.duration, serializer);
        <Option<String>>::sse_encode(self.album, serializer);
        <Vec<music_api::Quality>>::sse_encode(self.qualities, serializer);
        <Option<music_api::Quality>>::sse_encode(self.default_quality, serializer);
        <Option<String>>::sse_encode(self.art_pic, serializer);
        <Option<String>>::sse_encode(self.lyric, serializer);
    }
}

impl SseEncode for music_api::MusicListInfo {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <i64>::sse_encode(self.id, serializer);
        <String>::sse_encode(self.name, serializer);
        <String>::sse_encode(self.art_pic, serializer);
        <String>::sse_encode(self.desc, serializer);
        <Option<music_api::ExtraInfo>>::sse_encode(self.extra, serializer);
    }
}

impl SseEncode for crate::api::bind::factory_bind::OnlineFactoryW {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {}
}

impl SseEncode for Option<String> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <bool>::sse_encode(self.is_some(), serializer);
        if let Some(value) = self {
            <String>::sse_encode(value, serializer);
        }
    }
}

impl SseEncode for Option<MusicW> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <bool>::sse_encode(self.is_some(), serializer);
        if let Some(value) = self {
            <MusicW>::sse_encode(value, serializer);
        }
    }
}

impl SseEncode for Option<chrono::DateTime<chrono::Utc>> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <bool>::sse_encode(self.is_some(), serializer);
        if let Some(value) = self {
            <chrono::DateTime<chrono::Utc>>::sse_encode(value, serializer);
        }
    }
}

impl SseEncode for Option<crate::api::types::extern_api::ExternApi> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <bool>::sse_encode(self.is_some(), serializer);
        if let Some(value) = self {
            <crate::api::types::extern_api::ExternApi>::sse_encode(value, serializer);
        }
    }
}

impl SseEncode for Option<music_api::ExtraInfo> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <bool>::sse_encode(self.is_some(), serializer);
        if let Some(value) = self {
            <music_api::ExtraInfo>::sse_encode(value, serializer);
        }
    }
}

impl SseEncode for Option<music_api::MusicFuzzFilter> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <bool>::sse_encode(self.is_some(), serializer);
        if let Some(value) = self {
            <music_api::MusicFuzzFilter>::sse_encode(value, serializer);
        }
    }
}

impl SseEncode for Option<music_api::Quality> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <bool>::sse_encode(self.is_some(), serializer);
        if let Some(value) = self {
            <music_api::Quality>::sse_encode(value, serializer);
        }
    }
}

impl SseEncode for Option<crate::api::types::version::Release> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <bool>::sse_encode(self.is_some(), serializer);
        if let Some(value) = self {
            <crate::api::types::version::Release>::sse_encode(value, serializer);
        }
    }
}

impl SseEncode for Option<u32> {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <bool>::sse_encode(self.is_some(), serializer);
        if let Some(value) = self {
            <u32>::sse_encode(value, serializer);
        }
    }
}

impl SseEncode for crate::api::types::playinfo::PlayInfo {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <String>::sse_encode(self.uri, serializer);
        <music_api::Quality>::sse_encode(self.quality, serializer);
    }
}

impl SseEncode for music_api::Quality {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <String>::sse_encode(self.short, serializer);
        <Option<String>>::sse_encode(self.level, serializer);
        <Option<u32>>::sse_encode(self.bitrate, serializer);
        <Option<String>>::sse_encode(self.format, serializer);
        <Option<String>>::sse_encode(self.size, serializer);
    }
}

impl SseEncode for (MusicListW, Vec<MusicAggregatorW>) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <MusicListW>::sse_encode(self.0, serializer);
        <Vec<MusicAggregatorW>>::sse_encode(self.1, serializer);
    }
}

impl SseEncode for (String, String) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <String>::sse_encode(self.0, serializer);
        <String>::sse_encode(self.1, serializer);
    }
}

impl SseEncode for crate::api::types::version::Release {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        <String>::sse_encode(self.url, serializer);
        <String>::sse_encode(self.assets_url, serializer);
        <String>::sse_encode(self.upload_url, serializer);
        <String>::sse_encode(self.html_url, serializer);
        <u64>::sse_encode(self.id, serializer);
        <crate::api::types::version::Author>::sse_encode(self.author, serializer);
        <String>::sse_encode(self.node_id, serializer);
        <String>::sse_encode(self.tag_name, serializer);
        <String>::sse_encode(self.target_commitish, serializer);
        <String>::sse_encode(self.name, serializer);
        <bool>::sse_encode(self.draft, serializer);
        <bool>::sse_encode(self.prerelease, serializer);
        <String>::sse_encode(self.created_at, serializer);
        <String>::sse_encode(self.published_at, serializer);
        <Vec<crate::api::types::version::Asset>>::sse_encode(self.assets, serializer);
        <String>::sse_encode(self.tarball_url, serializer);
        <String>::sse_encode(self.zipball_url, serializer);
        <String>::sse_encode(self.body, serializer);
    }
}

impl SseEncode for crate::api::bind::factory_bind::SqlFactoryW {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {}
}

impl SseEncode for u32 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer.cursor.write_u32::<NativeEndian>(self).unwrap();
    }
}

impl SseEncode for u64 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer.cursor.write_u64::<NativeEndian>(self).unwrap();
    }
}

impl SseEncode for u8 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer.cursor.write_u8(self).unwrap();
    }
}

impl SseEncode for () {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {}
}

impl SseEncode for usize {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer
            .cursor
            .write_u64::<NativeEndian>(self as _)
            .unwrap();
    }
}

impl SseEncode for i32 {
    // Codec=Sse (Serialization based), see doc to use other codecs
    fn sse_encode(self, serializer: &mut flutter_rust_bridge::for_generated::SseSerializer) {
        serializer.cursor.write_i32::<NativeEndian>(self).unwrap();
    }
}

#[cfg(not(target_family = "wasm"))]
#[path = "frb_generated.io.rs"]
mod io;
#[cfg(not(target_family = "wasm"))]
pub use io::*;

/// cbindgen:ignore
#[cfg(target_family = "wasm")]
#[path = "frb_generated.web.rs"]
mod web;
#[cfg(target_family = "wasm")]
pub use web::*;
