[package]
name = "rust_lib_app_rhyme"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib", "staticlib"]

[dependencies]
anyhow = "1.0.86"
flutter_rust_bridge = { version = "=2.0.0", features = ["chrono"] }
lazy_static = "1.5.0"
music_api = { git = "https://github.com/canxin121/music_api" }
# music_api = {path = "D:\\Git\\music_api"}
rayon = "1.10.0"
sea-query = "0.30.7"
serde = "1.0.203"
sqlx = "0.7.4"
tokio = { version = "1.38.0", features = ["full"] }
reqwest = { version = "0.12.5", features = [
    "json",
    "rustls-tls",
    "rustls-tls-webpki-roots",
    "stream",
], default-features = false }
futures = "0.3.30"
reqwest-middleware = "0.3.1"
reqwest-retry = "0.6.0"
futures-util = "0.3.30"
serde_json = "1.0.119"
chrono = { version = "0.4.38", features = ["serde"] }
sha2 = "0.10.8"
base64 = "0.22.1"
rc4 = { git = "https://github.com/rsdump/rc4" }
