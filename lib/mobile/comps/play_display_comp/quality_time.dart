import 'package:app_rhyme/src/rust/api/bind/mirrors.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:app_rhyme/mobile/comps/chores/badge.dart';
import 'package:app_rhyme/services/js_music_source_service.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:app_rhyme/utils/time_parser.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:pull_down_button/pull_down_button.dart';

class QualityTime extends StatefulWidget {
  final double padding;
  final double fontHeight;
  const QualityTime({super.key, this.padding = 20.0, required this.fontHeight});

  @override
  State<StatefulWidget> createState() => QualityTimeState();
}

class QualityTimeState extends State<QualityTime> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: widget.padding, right: widget.padding),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Obx(() => SizedBox(
                width: 60,
                child: Text(
                  formatDuration(
                      globalAudioUiController.position.value.inSeconds),
                  textAlign: TextAlign.left,
                  style: TextStyle(
                    color: CupertinoColors.systemGrey6,
                    fontWeight: FontWeight.w300,
                    fontSize: widget.fontHeight,
                  ).useSystemChineseFont(),
                ),
              )),
          // 音质信息按钮
          GestureDetector(
            onTapDown: (details) {
              // 从JS音乐源获取支持的音质选项
              final jsService = JsMusicSourceService();
              final availableQualities = jsService.getAvailableQualities();

              if (availableQualities.isNotEmpty) {
                // 将字符串音质转换为Quality对象
                List<Quality> qualityOptions = availableQualities.map((qualityStr) {
                  return Quality(
                    short: qualityStr,
                    level: _getQualityLevel(qualityStr),
                    bitrate: _getBitrateFromQuality(qualityStr),
                    format: 'mp3',
                    size: null,
                  );
                }).toList();

                showPullDownMenu(
                    context: context,
                    items: qualitySelectPullDown(context, qualityOptions,
                        (selectQuality) async {
                      await globalAudioHandler
                          .replacePlayingMusic(selectQuality);
                    }),
                    position: details.globalPosition & Size.zero);
              } else {
                // 如果没有JS音乐源，使用原有的音质选项
                List<Quality>? qualityOptions =
                    globalAudioHandler.playingMusic.value?.info.qualities;
                if (qualityOptions != null && qualityOptions.isNotEmpty) {
                  showPullDownMenu(
                      context: context,
                      items: qualitySelectPullDown(context, qualityOptions,
                          (selectQuality) async {
                        await globalAudioHandler
                            .replacePlayingMusic(selectQuality);
                      }),
                      position: details.globalPosition & Size.zero);
                }
              }
            },
            child: Obx(() {
              final currentQuality = globalAudioHandler.playingMusic.value?.currentQuality.value;
              return Badge(
                isDarkMode: true,
                label: currentQuality != null
                    ? _getQualityLevel(currentQuality.short)
                    : "音质",
              );
            }),
          ),
          Obx(() => SizedBox(
                width: 60,
                child: Text(
                  formatDuration(
                      globalAudioUiController.duration.value.inSeconds),
                  textAlign: TextAlign.right,
                  style: TextStyle(
                    color: CupertinoColors.systemGrey6,
                    fontWeight: FontWeight.w300,
                    fontSize: widget.fontHeight,
                  ).useSystemChineseFont(),
                ),
              )),
        ],
      ),
    );
  }
}

/// 获取音质级别描述
String _getQualityLevel(String quality) {
  const qualityLevels = {
    // JS源中的音质映射
    'jymaster': '超清母带',
    'master': '超清母带',
    'sky': '沉浸环绕声',
    'hires': 'Hi-Res音质',
    'jyeffect': '高清环绕声',
    'surround': '环绕声',
    'lossless': '无损音质',
    'flac': '无损音质',
    'exhigh': '极高品质',
    '320k': '极高品质',
    'standard': '标准音质',
    '128k': '标准音质',

    // API返回的中文音质名称（直接使用）
    '极高音质': '极高音质',
    '标准音质': '标准音质',
    '无损音质': '无损音质',
    '超清母带': '超清母带',
    'Hi-Res音质': 'Hi-Res音质',
    '高清环绕声': '高清环绕声',
    '沉浸环绕声': '沉浸环绕声',

    // 其他可能的音质
    'higher': 'Hi-Res音质', // 修复：将"较高品质"改为"Hi-Res音质"
    'high': '高品质',
    'hight': '高品质', // 修复拼写错误的情况
  };
  return qualityLevels[quality] ?? quality;
}

/// 根据音质获取比特率
int? _getBitrateFromQuality(String quality) {
  const bitrateMap = {
    'jymaster': 2304,
    'master': 2304,
    'sky': 320,
    'hires': 2304,
    'jyeffect': 320,
    'surround': 320,
    'lossless': 1411,
    'flac': 1411,
    'exhigh': 320,
    '320k': 320,
    'standard': 128,
    '128k': 128,
  };
  return bitrateMap[quality];
}

/// 获取音质的完整显示名称（包含格式）
String _getQualityDisplayName(Quality quality) {
  final levelName = _getQualityLevel(quality.short);
  final bitrate = quality.bitrate ?? _getBitrateFromQuality(quality.short);
  final format = quality.format ?? 'mp3';

  if (bitrate != null) {
    return '$levelName ${bitrate}k$format';
  } else {
    return '$levelName $format';
  }
}

List<PullDownMenuEntry> qualitySelectPullDown(
        BuildContext context,
        List<Quality> qualitys,
        Future<void> Function(Quality selectQuality) onSelect) =>
    [
      PullDownMenuTitle(
          title: Text(
        "切换音质仅限当前",
        style: const TextStyle().useSystemChineseFont(),
      )),
      ...qualitys.map(
        (quality) => PullDownMenuItem(
            itemTheme: PullDownMenuItemTheme(
                textStyle: const TextStyle().useSystemChineseFont()),
            title: _getQualityLevel(quality.short),
            onTap: () async {
              await onSelect(quality);
            }),
      )
    ];
