import 'dart:async';
import 'package:app_rhyme/types/lyric_ui.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:app_rhyme/types/music_container.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:app_rhyme/utils/time_parser.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lyric/lyrics_reader.dart';
import 'package:get/get.dart';

class LyricDisplay extends StatefulWidget {
  final double maxHeight;
  final bool isDarkMode;
  const LyricDisplay(
      {super.key, required this.maxHeight, required this.isDarkMode});

  @override
  LyricDisplayState createState() => LyricDisplayState();
}

class LyricDisplayState extends State<LyricDisplay> {
  late LyricUI lyricUI;
  var lyricModel =
      LyricsModelBuilder.create().bindLyricToMain("[00:00.00]无歌词").getModel();
  late StreamSubscription<MusicContainer?> stream;
  bool _showTranslation = false;

  @override
  void initState() {
    super.initState();
    lyricUI = AppleMusicLyricUi();
    _updateLyricModel();
    stream = globalAudioHandler.playingMusic.listen((p0) {
      setState(() {
        _updateLyricModel();
      });
    });
  }

  void _updateLyricModel() async {
    final currentMusic = globalAudioHandler.playingMusic.value;
    String mainLyric = currentMusic?.info.lyric ?? "[00:00.00]无歌词";
    String? translationLyric;

    // 如果没有歌词或歌词为空，尝试从JS音乐源获取
    if (mainLyric.isEmpty || mainLyric == "[00:00.00]无歌词" || mainLyric == "null") {
      await _fetchLyricFromJsSource();
      // 重新获取更新后的歌词
      final updatedMusic = globalAudioHandler.playingMusic.value;
      mainLyric = updatedMusic?.info.lyric ?? "[00:00.00]无歌词";
    }

    // 尝试从JS音乐源获取翻译歌词
    if (_showTranslation) {
      translationLyric = await _fetchTranslationLyricFromJsSource();
    }

    if (_showTranslation && translationLyric != null && translationLyric.isNotEmpty) {
      lyricModel = LyricsModelBuilder.create()
          .bindLyricToMain(mainLyric)
          .bindLyricToExt(translationLyric)
          .getModel();
    } else {
      lyricModel = LyricsModelBuilder.create()
          .bindLyricToMain(mainLyric)
          .getModel();
    }

    // 强制刷新UI
    setState(() {});
  }

  /// 从JS音乐源获取歌词
  Future<void> _fetchLyricFromJsSource() async {
    final currentMusic = globalAudioHandler.playingMusic.value;
    if (currentMusic == null) return;

    try {
      // 强制刷新歌词
      await currentMusic.aggregator.fetchLyric();
      // 等待一小段时间确保歌词更新
      await Future.delayed(const Duration(milliseconds: 100));
    } catch (e) {
      print('[LyricDisplay] 获取歌词失败: $e');
    }
  }

  /// 从JS音乐源获取翻译歌词
  Future<String?> _fetchTranslationLyricFromJsSource() async {
    final currentMusic = globalAudioHandler.playingMusic.value;
    if (currentMusic == null) return null;

    try {
      // 这里需要实现从JS音乐源获取翻译歌词的逻辑
      // 暂时返回null，后续可以扩展
      return null;
    } catch (e) {
      print('[LyricDisplay] 获取翻译歌词失败: $e');
      return null;
    }
  }

  /// 切换翻译显示状态
  void toggleTranslation() {
    setState(() {
      _showTranslation = !_showTranslation;
      _updateLyricModel();
    });
  }

  @override
  void dispose() {
    stream.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 歌词翻译按钮
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              CupertinoButton(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                minSize: 0,
                onPressed: () {
                  setState(() {
                    _showTranslation = !_showTranslation;
                    _updateLyricModel();
                  });
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _showTranslation ? CupertinoIcons.eye_slash : CupertinoIcons.eye,
                      size: 16,
                      color: widget.isDarkMode ? CupertinoColors.white : CupertinoColors.black,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _showTranslation ? '隐藏翻译' : '显示翻译',
                      style: TextStyle(
                        fontSize: 12,
                        color: widget.isDarkMode ? CupertinoColors.white : CupertinoColors.black,
                      ).useSystemChineseFont(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        // 歌词显示区域
        Expanded(
          child: Obx(() => LyricsReader(
                playing: globalAudioHandler.playingMusic.value != null,
                emptyBuilder: () => Center(
                  child: Text(
                    "No lyrics",
                    style: lyricUI.getOtherMainTextStyle().useSystemChineseFont(),
                  ),
                ),
                model: lyricModel,
                position: globalAudioUiController.position.value.inMilliseconds,
                lyricUi: lyricUI,
                size: Size(double.infinity, widget.maxHeight - 50), // 减去按钮高度
                padding: const EdgeInsets.symmetric(horizontal: 40),
                selectLineBuilder: (progress, confirm) {
                  return Row(
                    children: [
                      IconButton(
                          onPressed: () {
                            var toSeek = Duration(milliseconds: progress);
                            globalAudioHandler.seek(toSeek).then((value) {
                              confirm.call();
                              // 这里是考虑到在暂停状态下。需要开启播放
                              if (!globalAudioHandler.isPlaying) {
                                globalAudioHandler.play();
                              }
                            });
                          },
                          icon: const Icon(Icons.play_arrow,
                              color: CupertinoColors.white)),
                      Expanded(
                        child: Container(
                          decoration:
                              const BoxDecoration(color: CupertinoColors.white),
                          height: 1,
                          width: double.infinity,
                        ),
                      ),
                      Text(
                        formatDuration(Duration(milliseconds: progress).inSeconds),
                        style: const TextStyle(color: CupertinoColors.white)
                            .useSystemChineseFont(),
                      )
                    ],
                  );
                },
              )),
        ),
      ],
    );
  }
}
