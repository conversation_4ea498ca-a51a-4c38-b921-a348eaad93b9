import 'dart:io';
import 'package:app_rhyme/utils/cache_helper.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:glassmorphism_ui/glassmorphism_ui.dart';

class MusicArtPic extends StatefulWidget {
  final EdgeInsets padding;
  const MusicArtPic({
    super.key,
    required this.padding,
  }); // 修改构造函数

  @override
  State<StatefulWidget> createState() => MusicArtPicState();
}

class MusicArtPicState extends State<MusicArtPic> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Obx(() {
          final currentMusic = globalAudioHandler.playingMusic.value;
          return Container(
            key: ValueKey("art_pic_${currentMusic?.hashCode ?? 'empty'}"),
            padding: widget.padding,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(18.0),
                boxShadow: [
                  BoxShadow(
                    color: Platform.isIOS
                        ? CupertinoColors.black.withOpacity(0.2)
                        : CupertinoColors.black.withOpacity(0.4),
                    blurRadius: Platform.isIOS ? 6 : 16,
                    spreadRadius: Platform.isIOS ? 1 : 2,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(18.0),
                child: Hero(
                  tag: "main_art_pic_${currentMusic?.info.name ?? 'empty'}_${DateTime.now().millisecondsSinceEpoch}",
                  child: imageCacheHelper(currentMusic?.info.artPic),
                ),
              ),
            ),
          );
        }));
  }
}
