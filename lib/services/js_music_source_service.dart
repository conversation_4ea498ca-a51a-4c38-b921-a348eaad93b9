import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import '../types/chore.dart';
import '../utils/global_vars.dart';
import '../src/rust/api/bind/mirrors.dart';
import '../src/rust/api/types/playinfo.dart';

/// JS音乐源服务
class JsMusicSourceService {
  static final JsMusicSourceService _instance = JsMusicSourceService._internal();
  factory JsMusicSourceService() => _instance;
  JsMusicSourceService._internal();

  List<JsMusicSourceData> _sources = [];
  bool _initialized = false;

  /// 获取初始化状态
  bool get isInitialized => _initialized;

  /// 初始化JS音乐源服务
  Future<void> initialize() async {
    if (_initialized) return;
    
    try {
      await _loadSources();
      _initialized = true;
      globalTalker.info('[JsMusicSourceService] Initialized with ${_sources.length} sources');
    } catch (e) {
      globalTalker.error('[JsMusicSourceService] Failed to initialize: $e');
    }
  }

  /// 加载已保存的JS音乐源
  Future<void> _loadSources() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sourcesJson = prefs.getStringList('js_music_sources') ?? [];
      
      _sources = sourcesJson.map((json) {
        try {
          final data = jsonDecode(json);
          return JsMusicSourceData.fromJson(data);
        } catch (e) {
          globalTalker.error('[JsMusicSourceService] Failed to parse source: $e');
          return null;
        }
      }).where((source) => source != null).cast<JsMusicSourceData>().toList();
      
      globalTalker.info('[JsMusicSourceService] Loaded ${_sources.length} sources');
    } catch (e) {
      globalTalker.error('[JsMusicSourceService] Failed to load sources: $e');
      _sources = [];
    }
  }

  /// 获取所有已加载JS音乐源支持的音质选项
  List<String> getAvailableQualities() {
    if (!_initialized) return [];

    Set<String> allQualities = {};

    for (final source in _sources) {
      if (source.status == JsSourceStatus.loaded && source.config != null) {
        allQualities.addAll(source.config!.supportedQualities);
      }
    }

    // 按优先级排序音质
    List<String> sortedQualities = [];
    const qualityPriority = [
      'jymaster', 'master', 'sky', 'hires', 'jyeffect', 'surround',
      'lossless', 'flac', 'exhigh', '320k', 'standard', '128k'
    ];

    for (String quality in qualityPriority) {
      if (allQualities.contains(quality)) {
        sortedQualities.add(quality);
      }
    }

    // 添加任何未在优先级列表中的音质
    for (String quality in allQualities) {
      if (!sortedQualities.contains(quality)) {
        sortedQualities.add(quality);
      }
    }

    globalTalker.info('[JsMusicSourceService] Available qualities: $sortedQualities');
    return sortedQualities;
  }

  /// 搜索音乐
  Future<List<MusicInfo>> searchMusic(String keyword, {int page = 1, int limit = 20}) async {
    if (!_initialized) await initialize();

    List<MusicInfo> allResults = [];

    for (final source in _sources) {
      if (source.status != JsSourceStatus.loaded) continue;

      try {
        final results = await _searchWithSource(source, keyword, page, limit);
        allResults.addAll(results);
      } catch (e) {
        globalTalker.error('[JsMusicSourceService] Search failed for ${source.config?.name}: $e');
      }
    }

    return allResults;
  }

  /// 使用指定源搜索音乐
  Future<List<MusicInfo>> _searchWithSource(JsMusicSourceData source, String keyword, int page, int limit) async {
    try {
      globalTalker.info('[JsMusicSourceService] Searching with ${source.config?.name}: $keyword');

      // 使用网易云音乐搜索API
      final searchUrl = 'https://api.kxzjoker.cn/api/163_search?name=${Uri.encodeComponent(keyword)}&limit=$limit';

      final response = await http.get(
        Uri.parse(searchUrl),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Referer': 'https://music.163.com/',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['code'] == 200 && data['data'] != null) {
          List<MusicInfo> results = [];

          for (final item in data['data']) {
            // 解析艺术家列表
            List<String> artists = [];
            if (item['artists'] != null) {
              for (final artist in item['artists']) {
                artists.add(artist['name'] ?? '');
              }
            }

            // 解析时长
            int duration = _parseDuration(item['duration'] ?? '');

            // 创建包含真实网易云音乐ID的extra信息
            final extraInfo = jsonEncode({
              'netease_id': item['id'],
              'album_id': item['album']?['id'],
              'artist_ids': item['artists']?.map((artist) => artist['id']).toList(),
              'publish_time': item['album']?['publishTime'],
            });

            // 获取真实的专辑封面
            String? artPic;
            if (item['album']?['id'] != null) {
              // 使用网易云音乐的专辑封面API
              artPic = 'https://p1.music.126.net/${_generateImageId(item['album']['id'])}/109951163077692.jpg';
            }
            artPic ??= 'https://p1.music.126.net/6y-UleORITEDbvrOLV0Q8A==/5639395138885805.jpg'; // 默认封面

            // 创建MusicInfo对象
            final musicInfo = MusicInfo(
              id: (item['id'] ?? 0) as int, // 使用真实的网易云音乐ID
              source: 'NetEase', // 设置为网易云音乐源
              name: item['name'] ?? '',
              artist: artists,
              album: item['album']?['name'] ?? '',
              artPic: artPic,
              duration: duration > 0 ? duration : null,
              lyric: null,
              qualities: [
                Quality(short: 'standard', level: '标准音质', bitrate: 128, format: 'mp3', size: null),
                Quality(short: 'exhigh', level: '极高品质', bitrate: 320, format: 'mp3', size: null),
                Quality(short: 'lossless', level: '无损音质', bitrate: 1411, format: 'flac', size: null),
              ],
            );

            results.add(musicInfo);
          }

          globalTalker.info('[JsMusicSourceService] Search successful: ${results.length} results');
          return results;
        }
      }

      globalTalker.warning('[JsMusicSourceService] Search failed for ${source.config?.name}');
      return [];

    } catch (e) {
      globalTalker.error('[JsMusicSourceService] Search error: $e');
      return [];
    }
  }

  /// 解析时长字符串为秒数
  int _parseDuration(String durationStr) {
    if (durationStr.isEmpty) return 0;

    final parts = durationStr.split(':');
    if (parts.length == 2) {
      final minutes = int.tryParse(parts[0]) ?? 0;
      final seconds = int.tryParse(parts[1]) ?? 0;
      return minutes * 60 + seconds;
    } else if (parts.length == 3) {
      final hours = int.tryParse(parts[0]) ?? 0;
      final minutes = int.tryParse(parts[1]) ?? 0;
      final seconds = int.tryParse(parts[2]) ?? 0;
      return hours * 3600 + minutes * 60 + seconds;
    }

    return 0;
  }

  /// 生成网易云音乐图片ID
  String _generateImageId(int albumId) {
    // 这是一个简化的实现，实际的网易云音乐图片ID生成算法更复杂
    // 这里使用专辑ID的哈希值作为图片ID
    final hash = albumId.hashCode.abs();
    return hash.toString().padLeft(18, '0');
  }

  /// 获取网易云音乐歌词
  Future<String?> _getNetEaseLyric(String musicId) async {
    try {
      globalTalker.info('[JsMusicSourceService] 获取歌词: $musicId');

      // 使用网易云音乐歌词API
      final lyricUrl = 'https://music.163.com/api/song/lyric?id=$musicId&lv=1&kv=1&tv=-1';

      final response = await http.get(
        Uri.parse(lyricUrl),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Referer': 'https://music.163.com/',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['lrc'] != null && data['lrc']['lyric'] != null) {
          String lyric = data['lrc']['lyric'];
          if (lyric.isNotEmpty) {
            globalTalker.info('[JsMusicSourceService] 歌词获取成功');
            return lyric;
          }
        }
      }

      globalTalker.warning('[JsMusicSourceService] 歌词获取失败');
      return null;

    } catch (e) {
      globalTalker.error('[JsMusicSourceService] 歌词获取异常: $e');
      return null;
    }
  }

  /// 获取播放信息
  Future<PlayInfo?> getPlayInfo(MusicInfo musicInfo, Quality quality, String? extra) async {
    if (!_initialized) await initialize();

    for (final source in _sources) {
      if (source.status != JsSourceStatus.loaded) continue;

      try {
        final playInfo = await _getPlayInfoWithSource(source, musicInfo, quality, extra);
        if (playInfo != null) {
          return playInfo;
        }
      } catch (e) {
        globalTalker.error('[JsMusicSourceService] Get play info failed for ${source.config?.name}: $e');
      }
    }

    return null;
  }

  /// JS音乐源专注于播放链接解析，搜索功能已移除

  /// 使用指定源获取播放信息
  Future<PlayInfo?> _getPlayInfoWithSource(JsMusicSourceData source, MusicInfo musicInfo, Quality quality, String? extra) async {
    try {
      globalTalker.info('[JsMusicSourceService] Getting play info with ${source.config?.name}: ${musicInfo.name}');

      // 实现一个简化的播放链接获取方案
      // 这里可以根据不同的音乐源实现不同的策略

      if (source.config?.name.contains('NetEase') == true || source.config?.name.contains('网易') == true) {
        return await _getNetEasePlayInfo(musicInfo, quality, extra);
      }

      // 对于其他JS源，暂时返回null
      // 实际应用中可以实现更多的音乐源支持
      globalTalker.warning('[JsMusicSourceService] Unsupported source: ${source.config?.name}');
      return null;

    } catch (e) {
      globalTalker.error('[JsMusicSourceService] Get play info error: $e');
      return null;
    }
  }

  /// 获取网易云音乐播放信息
  Future<PlayInfo?> _getNetEasePlayInfo(MusicInfo musicInfo, Quality quality, String? extra) async {
    try {
      globalTalker.info('[JsMusicSourceService] Getting NetEase play info for: ${musicInfo.name}');
      globalTalker.info('[JsMusicSourceService] Extra info: $extra');

      // 使用kxzjoker API获取播放信息
      final apiUrl = 'https://api.kxzjoker.cn/api/163_music';

      // 映射音质 - 直接使用设置页的音质，而不是传入的quality参数
      String selectedQuality = _getCurrentQuality();
      String apiQuality = _mapQualityToApi(selectedQuality);
      globalTalker.info('[JsMusicSourceService] 使用设置页音质: $selectedQuality -> API音质: $apiQuality');

      // 构建请求参数
      Map<String, String> params = {
        'level': apiQuality,
        'type': 'json',
      };

      // 尝试从extra字段获取真实的网易云音乐ID
      String? realMusicId = _extractNetEaseIdFromExtra(extra);

      // 如果extra中没有ID，尝试使用MusicInfo的ID（现在应该是真实的网易云音乐ID）
      if (realMusicId == null || realMusicId.isEmpty) {
        String musicId = musicInfo.id.toString();
        if (musicId != '0' && musicId.isNotEmpty && musicId != 'null') {
          realMusicId = musicId;
          globalTalker.info('[JsMusicSourceService] 使用MusicInfo中的网易云音乐ID: $realMusicId');
        }
      }

      // 如果仍然没有ID，尝试从音乐名称和艺术家搜索获取ID
      if (realMusicId == null || realMusicId.isEmpty) {
        realMusicId = await _searchNetEaseMusicId(musicInfo.name, musicInfo.artist.join(' '));
      }

      if (realMusicId != null && realMusicId.isNotEmpty) {
        globalTalker.info('[JsMusicSourceService] 最终使用的网易云音乐ID: $realMusicId');
        params['ids'] = realMusicId;
      } else {
        globalTalker.warning('[JsMusicSourceService] 无法获取有效的网易云音乐ID，歌曲: ${musicInfo.name} - ${musicInfo.artist.join(',')}');
        throw Exception('无法获取有效的网易云音乐ID: ${musicInfo.name}');
      }

      // 构建完整URL
      final uri = Uri.parse(apiUrl).replace(queryParameters: params);
      globalTalker.info('[JsMusicSourceService] Request URL: $uri');

      // 发送HTTP请求
      final response = await http.get(
        uri,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      );

      if (response.statusCode != 200) {
        throw Exception('HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }

      final data = jsonDecode(response.body);
      // 不显示完整的API响应，避免歌词内容污染日志
      globalTalker.info('[JsMusicSourceService] API Response: {status: ${data['status']}, name: ${data['name']}, level: ${data['level']}}');

      if (data['status'] != 200 || data['url'] == null || data['url'].isEmpty) {
        throw Exception('API返回错误: ${data['status'] ?? '未知错误'}');
      }

      // 尝试获取歌词
      String? lyric = await _getNetEaseLyric(realMusicId!);

      // 更新MusicInfo的歌词
      if (lyric != null && lyric.isNotEmpty) {
        musicInfo.lyric = lyric;
        globalTalker.info('[JsMusicSourceService] 获取歌词成功');
      }

      // 构造PlayInfo对象
      final playInfo = PlayInfo(
        uri: data['url'],
        quality: Quality(
          short: selectedQuality,  // 使用实际选择的音质
          level: data['level'],
          bitrate: _getBitrateFromQuality(selectedQuality),  // 使用实际选择的音质
          format: _getFormatFromUrl(data['url']),
          size: data['size'],
        ),
      );

      globalTalker.info('[JsMusicSourceService] Successfully got play info: ${playInfo.uri}');
      return playInfo;

    } catch (e) {
      globalTalker.error('[JsMusicSourceService] NetEase play info error: $e');
      return null;
    }
  }

  /// 获取当前应该使用的音质
  String _getCurrentQuality() {
    // 获取设置中的音质
    String selectedQuality;
    if (Platform.isIOS || Platform.isAndroid) {
      switch (globalConnectivityStateSimple) {
        case ConnectivityStateSimple.wifi:
          selectedQuality = globalConfig.wifiAutoQuality;
          break;
        case ConnectivityStateSimple.mobile:
          selectedQuality = globalConfig.mobileAutoQuality;
          break;
        case ConnectivityStateSimple.none:
          selectedQuality = 'lossless'; // 默认无损音质
          break;
      }
    } else {
      selectedQuality = globalConfig.wifiAutoQuality;
    }

    // 检查选择的音质是否在可用列表中
    final availableQualities = getAvailableQualities();
    if (availableQualities.contains(selectedQuality)) {
      return selectedQuality;
    }

    // 如果不在，选择第一个可用的音质
    return availableQualities.isNotEmpty ? availableQualities.first : 'lossless';
  }

  /// 映射音质到API参数
  String _mapQualityToApi(String quality) {
    const qualityMap = {
      '128k': 'standard',
      'standard': 'standard',
      '320k': 'exhigh',
      'exhigh': 'exhigh',
      'flac': 'lossless',
      'lossless': 'lossless',
      'hires': 'hires',
      'surround': 'jyeffect',
      'jyeffect': 'jyeffect',
      'sky': 'sky',
      'master': 'jymaster',
      'jymaster': 'jymaster',
    };
    return qualityMap[quality] ?? 'exhigh';
  }

  /// 根据音质获取比特率
  int? _getBitrateFromQuality(String quality) {
    const bitrateMap = {
      '128k': 128,
      '320k': 320,
      'flac': 1411,
      'hires': 2304,
      'surround': 320,
      'sky': 320,
      'master': 2304,
    };
    return bitrateMap[quality];
  }

  /// 从URL获取音频格式
  String? _getFormatFromUrl(String url) {
    if (url.contains('.flac')) return 'flac';
    if (url.contains('.mp3')) return 'mp3';
    if (url.contains('.m4a')) return 'm4a';
    return 'mp3';
  }

  /// 从extra字段提取网易云音乐ID
  String? _extractNetEaseIdFromExtra(String? extra) {
    if (extra == null || extra.isEmpty) return null;

    try {
      // extra字段可能包含JSON格式的数据或者直接是ID
      // 尝试多种格式解析

      // 1. 直接是数字ID
      if (RegExp(r'^\d+$').hasMatch(extra)) {
        return extra;
      }

      // 2. 包含ID的字符串，如 "id:123456" 或 "netease_id:123456"
      final idMatch = RegExp(r'(?:id|netease_id|music_id)[:\s=]+(\d+)', caseSensitive: false).firstMatch(extra);
      if (idMatch != null) {
        return idMatch.group(1);
      }

      // 3. URL格式，如 "https://music.163.com/song?id=123456"
      final urlMatch = RegExp(r'(?:music\.163\.com|y\.music\.163\.com).*[?&]id=(\d+)').firstMatch(extra);
      if (urlMatch != null) {
        return urlMatch.group(1);
      }

      // 4. JSON格式
      try {
        final json = jsonDecode(extra);
        if (json is Map) {
          // 尝试多个可能的键名
          for (final key in ['id', 'music_id', 'netease_id', 'song_id']) {
            if (json.containsKey(key)) {
              return json[key].toString();
            }
          }
        }
      } catch (e) {
        // JSON解析失败，继续其他方法
      }

      // 5. 从任何地方提取数字ID
      final numberMatch = RegExp(r'\b(\d{6,})\b').firstMatch(extra);
      if (numberMatch != null) {
        return numberMatch.group(1);
      }

      globalTalker.warning('[JsMusicSourceService] 无法从extra字段提取网易云音乐ID: $extra');
      return null;

    } catch (e) {
      globalTalker.error('[JsMusicSourceService] 解析extra字段时出错: $e');
      return null;
    }
  }

  /// 通过搜索获取网易云音乐ID
  Future<String?> _searchNetEaseMusicId(String songName, String artist) async {
    try {
      globalTalker.info('[JsMusicSourceService] 搜索网易云音乐ID: $songName - $artist');

      // 使用网易云音乐搜索API（这里使用一个简化的实现）
      // 实际应用中可能需要使用更完整的搜索API
      final searchQuery = Uri.encodeComponent('$songName $artist');
      final searchUrl = 'https://music.163.com/api/search/get/web?csrf_token=&hlpretag=&hlposttag=&s=$searchQuery&type=1&offset=0&total=true&limit=1';

      final response = await http.get(
        Uri.parse(searchUrl),
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Referer': 'https://music.163.com/',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['result'] != null && data['result']['songs'] != null && data['result']['songs'].isNotEmpty) {
          final firstSong = data['result']['songs'][0];
          final musicId = firstSong['id'].toString();
          globalTalker.info('[JsMusicSourceService] 搜索到网易云音乐ID: $musicId');
          return musicId;
        }
      }

      globalTalker.warning('[JsMusicSourceService] 搜索网易云音乐ID失败: $songName - $artist');
      return null;

    } catch (e) {
      globalTalker.error('[JsMusicSourceService] 搜索网易云音乐ID异常: $e');
      return null;
    }
  }
}

/// JS音乐源数据
class JsMusicSourceData {
  final String id;
  final String url;
  final String localPath;
  final JsSourceConfig? config;
  final JsSourceStatus status;
  final String? errorMessage;
  final DateTime lastUpdate;

  const JsMusicSourceData({
    required this.id,
    required this.url,
    required this.localPath,
    this.config,
    required this.status,
    this.errorMessage,
    required this.lastUpdate,
  });

  factory JsMusicSourceData.fromJson(Map<String, dynamic> json) {
    return JsMusicSourceData(
      id: json['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
      url: json['url'] ?? '',
      localPath: json['localPath'] ?? '',
      config: json['config'] != null ? JsSourceConfig.fromJson(json['config']) : null,
      status: JsSourceStatus.values[json['status'] ?? 0],
      errorMessage: json['errorMessage'],
      lastUpdate: DateTime.parse(json['lastUpdate'] ?? DateTime.now().toIso8601String()),
    );
  }
}

/// JS音乐源配置
class JsSourceConfig {
  final String name;
  final String version;
  final String author;
  final String description;
  final List<String> supportedQualities;

  const JsSourceConfig({
    required this.name,
    required this.version,
    required this.author,
    required this.description,
    required this.supportedQualities,
  });

  factory JsSourceConfig.fromJson(Map<String, dynamic> json) {
    return JsSourceConfig(
      name: json['name'] ?? '未知音乐源',
      version: json['version'] ?? '1.0.0',
      author: json['author'] ?? '未知作者',
      description: json['description'] ?? '无描述',
      supportedQualities: List<String>.from(json['supportedQualities'] ?? ['128k', '320k']),
    );
  }
}

/// JS音乐源状态
enum JsSourceStatus {
  loading,
  loaded,
  error,
}
