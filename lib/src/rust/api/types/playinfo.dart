// This file is automatically generated, so please do not edit it.
// Generated by `flutter_rust_bridge`@ 2.0.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import '../bind/mirrors.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

// These function are ignored because they are on traits that is not defined in current crate (put an empty `#[frb]` on it to unignore): `clone`

class PlayInfo {
  final String uri;
  final Quality quality;

  const PlayInfo({
    required this.uri,
    required this.quality,
  });

  @override
  int get hashCode => uri.hashCode ^ quality.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PlayInfo &&
          runtimeType == other.runtimeType &&
          uri == other.uri &&
          quality == other.quality;
}
