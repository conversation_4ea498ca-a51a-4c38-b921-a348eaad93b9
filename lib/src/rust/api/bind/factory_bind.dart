// This file is automatically generated, so please do not edit it.
// Generated by `flutter_rust_bridge`@ 2.0.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'mirrors.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'type_bind.dart';

class AggregatorOnlineFactoryW {
  const AggregatorOnlineFactoryW();

  static Future<List<MusicAggregatorW>> searchMusicAggregator(
          {required List<MusicAggregatorW> aggregators,
          required List<String> sources,
          required String content,
          required int page,
          required int limit,
          MusicFuzzFilter? filter}) =>
      RustLib.instance.api
          .crateApiBindFactoryBindAggregatorOnlineFactoryWSearchMusicAggregator(
              aggregators: aggregators,
              sources: sources,
              content: content,
              page: page,
              limit: limit,
              filter: filter);

  @override
  int get hashCode => 0;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AggregatorOnlineFactoryW && runtimeType == other.runtimeType;
}

class OnlineFactoryW {
  const OnlineFactoryW();

  static Future<(MusicListW, List<MusicAggregatorW>)> getMusiclistFromShare(
          {required String shareUrl}) =>
      RustLib.instance.api
          .crateApiBindFactoryBindOnlineFactoryWGetMusiclistFromShare(
              shareUrl: shareUrl);

  static Future<List<MusicListW>> searchMusiclist(
          {required List<String> sources,
          required String content,
          required int page,
          required int limit}) =>
      RustLib.instance.api.crateApiBindFactoryBindOnlineFactoryWSearchMusiclist(
          sources: sources, content: content, page: page, limit: limit);

  @override
  int get hashCode => 0;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OnlineFactoryW && runtimeType == other.runtimeType;
}

class SqlFactoryW {
  const SqlFactoryW();

  static Future<void> addMusics(
          {required String musicsListName,
          required List<MusicAggregatorW> musics}) =>
      RustLib.instance.api.crateApiBindFactoryBindSqlFactoryWAddMusics(
          musicsListName: musicsListName, musics: musics);

  static Future<void> changeMusicDefaultSource(
          {required String musicListName,
          required Int64List ids,
          required List<String> newDefaultSources}) =>
      RustLib.instance.api
          .crateApiBindFactoryBindSqlFactoryWChangeMusicDefaultSource(
              musicListName: musicListName,
              ids: ids,
              newDefaultSources: newDefaultSources);

  static Future<void> changeMusicInfo(
          {required List<MusicW> musics, required List<MusicInfo> newInfos}) =>
      RustLib.instance.api.crateApiBindFactoryBindSqlFactoryWChangeMusicInfo(
          musics: musics, newInfos: newInfos);

  static Future<void> changeMusiclistInfo(
          {required List<MusicListInfo> old,
          required List<MusicListInfo> new_}) =>
      RustLib.instance.api
          .crateApiBindFactoryBindSqlFactoryWChangeMusiclistInfo(
              old: old, new_: new_);

  static Future<void> cleanUnusedMusicData() => RustLib.instance.api
      .crateApiBindFactoryBindSqlFactoryWCleanUnusedMusicData();

  static Future<void> cleanUnusedMusiclist() => RustLib.instance.api
      .crateApiBindFactoryBindSqlFactoryWCleanUnusedMusiclist();

  static Future<void> createMusiclist(
          {required List<MusicListInfo> musicListInfos}) =>
      RustLib.instance.api.crateApiBindFactoryBindSqlFactoryWCreateMusiclist(
          musicListInfos: musicListInfos);

  static Future<void> delDuplicateMusicsOfMusiclist(
          {required MusicListInfo musiclistInfo}) =>
      RustLib.instance.api
          .crateApiBindFactoryBindSqlFactoryWDelDuplicateMusicsOfMusiclist(
              musiclistInfo: musiclistInfo);

  static Future<void> delMusiclist({required List<String> musiclistNames}) =>
      RustLib.instance.api.crateApiBindFactoryBindSqlFactoryWDelMusiclist(
          musiclistNames: musiclistNames);

  static Future<void> delMusics(
          {required String musicListName, required Int64List ids}) =>
      RustLib.instance.api.crateApiBindFactoryBindSqlFactoryWDelMusics(
          musicListName: musicListName, ids: ids);

  static Future<List<MusicListW>> getAllMusiclists() =>
      RustLib.instance.api.crateApiBindFactoryBindSqlFactoryWGetAllMusiclists();

  static Future<List<MusicAggregatorW>> getAllMusics(
          {required MusicListInfo musiclistInfo}) =>
      RustLib.instance.api.crateApiBindFactoryBindSqlFactoryWGetAllMusics(
          musiclistInfo: musiclistInfo);

  static Future<MusicAggregatorW> getMusicById(
          {required MusicListInfo musicListInfo,
          required PlatformInt64 id,
          required List<String> sources}) =>
      RustLib.instance.api.crateApiBindFactoryBindSqlFactoryWGetMusicById(
          musicListInfo: musicListInfo, id: id, sources: sources);

  static Future<void> initFromPath({required String filepath}) =>
      RustLib.instance.api
          .crateApiBindFactoryBindSqlFactoryWInitFromPath(filepath: filepath);

  static Future<List<MusicW>> readMusicData({required String source}) =>
      RustLib.instance.api
          .crateApiBindFactoryBindSqlFactoryWReadMusicData(source: source);

  static Future<void> reorderMusiclist({required Int64List newIds}) =>
      RustLib.instance.api
          .crateApiBindFactoryBindSqlFactoryWReorderMusiclist(newIds: newIds);

  static Future<void> reorderMusics(
          {required String musicListName, required Int64List newIds}) =>
      RustLib.instance.api.crateApiBindFactoryBindSqlFactoryWReorderMusics(
          musicListName: musicListName, newIds: newIds);

  static Future<void> replaceMusics(
          {required String musicListName,
          required Int64List ids,
          required List<MusicAggregatorW> musics}) =>
      RustLib.instance.api.crateApiBindFactoryBindSqlFactoryWReplaceMusics(
          musicListName: musicListName, ids: ids, musics: musics);

  static Future<void> shutdown() =>
      RustLib.instance.api.crateApiBindFactoryBindSqlFactoryWShutdown();

  @override
  int get hashCode => 0;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SqlFactoryW && runtimeType == other.runtimeType;
}
