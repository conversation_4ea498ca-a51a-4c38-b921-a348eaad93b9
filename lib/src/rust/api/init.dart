// This file is automatically generated, so please do not edit it.
// Generated by `flutter_rust_bridge`@ 2.0.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'types/config.dart';
import 'types/extern_api.dart';

Future<Config> initBackend({required String storeRoot}) =>
    RustLib.instance.api.crateApiInitInitBackend(storeRoot: storeRoot);
