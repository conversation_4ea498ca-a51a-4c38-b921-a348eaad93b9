// This file is automatically generated, so please do not edit it.
// Generated by `flutter_rust_bridge`@ 2.0.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import '../bind/mirrors.dart';
import '../types/playinfo.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

// These functions are ignored because they are not marked as `pub`: `gen_json_file_name`, `gen_music_path`

Future<bool> hasCachePlayinfo({required MusicInfo musicInfo}) =>
    RustLib.instance.api
        .crateApiCacheMusicCacheHasCachePlayinfo(musicInfo: musicInfo);

Future<PlayInfo> getCachePlayinfo({required MusicInfo musicInfo}) =>
    RustLib.instance.api
        .crateApiCacheMusicCacheGetCachePlayinfo(musicInfo: musicInfo);

Future<void> cacheMusic(
        {required MusicInfo musicInfo, required PlayInfo playinfo}) =>
    RustLib.instance.api.crateApiCacheMusicCacheCacheMusic(
        musicInfo: musicInfo, playinfo: playinfo);

Future<void> deleteMusicCache({required MusicInfo musicInfo}) =>
    RustLib.instance.api
        .crateApiCacheMusicCacheDeleteMusicCache(musicInfo: musicInfo);
