// This file is automatically generated, so please do not edit it.
// Generated by `flutter_rust_bridge`@ 2.0.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

Future<String> genHash({required String str}) =>
    RustLib.instance.api.crateApiCacheFileCacheGenHash(str: str);

Future<String> cacheFile(
        {required String file,
        required String cachePath,
        String? filename,
        String? exportRoot}) =>
    RustLib.instance.api.crateApiCacheFileCacheCacheFile(
        file: file,
        cachePath: cachePath,
        filename: filename,
        exportRoot: exportRoot);

String? useCacheFile(
        {required String file,
        required String cachePath,
        String? filename,
        String? exportRoot}) =>
    RustLib.instance.api.crateApiCacheFileCacheUseCacheFile(
        file: file,
        cachePath: cachePath,
        filename: filename,
        exportRoot: exportRoot);

Future<void> deleteCacheFile(
        {required String file,
        required String cachePath,
        String? filename,
        String? exportRoot}) =>
    RustLib.instance.api.crateApiCacheFileCacheDeleteCacheFile(
        file: file,
        cachePath: cachePath,
        filename: filename,
        exportRoot: exportRoot);
