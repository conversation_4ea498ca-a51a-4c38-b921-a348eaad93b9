// This file is automatically generated, so please do not edit it.
// Generated by `flutter_rust_bridge`@ 2.0.0.

// ignore_for_file: invalid_use_of_internal_member, unused_import, unnecessary_import

import '../../frb_generated.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

// These functions are ignored because they are not marked as `pub`: `del_old_data`

Future<void> rename({required String from, required String to}) =>
    RustLib.instance.api.crateApiCacheFsUtilRename(from: from, to: to);

Future<void> copyFile({required String from, required String to}) =>
    RustLib.instance.api.crateApiCacheFsUtilCopyFile(from: from, to: to);

Future<void> copyDirectory({required String src, required String dst}) =>
    RustLib.instance.api.crateApiCacheFsUtilCopyDirectory(src: src, dst: dst);

Future<void> removeDir({required String dir}) =>
    RustLib.instance.api.crateApiCacheFsUtilRemoveDir(dir: dir);

Future<void> removeFile({required String file}) =>
    RustLib.instance.api.crateApiCacheFsUtilRemoveFile(file: file);
