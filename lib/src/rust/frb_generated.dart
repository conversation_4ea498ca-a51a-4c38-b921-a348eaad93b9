// This file is automatically generated, so please do not edit it.
// Generated by `flutter_rust_bridge`@ 2.0.0.

// ignore_for_file: unused_import, unused_element, unnecessary_import, duplicate_ignore, invalid_use_of_internal_member, annotate_overrides, non_constant_identifier_names, curly_braces_in_flow_control_structures, prefer_const_literals_to_create_immutables, unused_field

import 'api/bind/factory_bind.dart';
import 'api/bind/mirrors.dart';
import 'api/bind/type_bind.dart';
import 'api/cache/file_cache.dart';
import 'api/cache/fs_util.dart';
import 'api/cache/music_cache.dart';
import 'api/init.dart';
import 'api/types/config.dart';
import 'api/types/extern_api.dart';
import 'api/types/playinfo.dart';
import 'api/types/version.dart';
import 'api/utils/crypto.dart';
import 'api/utils/http_helper.dart';
import 'api/utils/path_util.dart';
import 'dart:async';
import 'dart:convert';
import 'frb_generated.dart';
import 'frb_generated.io.dart'
    if (dart.library.js_interop) 'frb_generated.web.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

/// Main entrypoint of the Rust API
class RustLib extends BaseEntrypoint<RustLibApi, RustLibApiImpl, RustLibWire> {
  @internal
  static final instance = RustLib._();

  RustLib._();

  /// Initialize flutter_rust_bridge
  static Future<void> init({
    RustLibApi? api,
    BaseHandler? handler,
    ExternalLibrary? externalLibrary,
  }) async {
    await instance.initImpl(
      api: api,
      handler: handler,
      externalLibrary: externalLibrary,
    );
  }

  /// Dispose flutter_rust_bridge
  ///
  /// The call to this function is optional, since flutter_rust_bridge (and everything else)
  /// is automatically disposed when the app stops.
  static void dispose() => instance.disposeImpl();

  @override
  ApiImplConstructor<RustLibApiImpl, RustLibWire> get apiImplConstructor =>
      RustLibApiImpl.new;

  @override
  WireConstructor<RustLibWire> get wireConstructor =>
      RustLibWire.fromExternalLibrary;

  @override
  Future<void> executeRustInitializers() async {
    await api.crateApiInitInit();
  }

  @override
  ExternalLibraryLoaderConfig get defaultExternalLibraryLoaderConfig =>
      kDefaultExternalLibraryLoaderConfig;

  @override
  String get codegenVersion => '2.0.0';

  @override
  int get rustContentHash => 402443197;

  static const kDefaultExternalLibraryLoaderConfig =
      ExternalLibraryLoaderConfig(
    stem: 'rust_lib_app_rhyme',
    ioDirectory: 'rust/target/release/',
    webPrefix: 'pkg/',
  );
}

abstract class RustLibApi extends BaseApi {
  Future<List<MusicAggregatorW>>
      crateApiBindFactoryBindAggregatorOnlineFactoryWSearchMusicAggregator(
          {required List<MusicAggregatorW> aggregators,
          required List<String> sources,
          required String content,
          required int page,
          required int limit,
          MusicFuzzFilter? filter});

  Future<(MusicListW, List<MusicAggregatorW>)>
      crateApiBindFactoryBindOnlineFactoryWGetMusiclistFromShare(
          {required String shareUrl});

  Future<List<MusicListW>> crateApiBindFactoryBindOnlineFactoryWSearchMusiclist(
      {required List<String> sources,
      required String content,
      required int page,
      required int limit});

  Future<void> crateApiBindFactoryBindSqlFactoryWAddMusics(
      {required String musicsListName, required List<MusicAggregatorW> musics});

  Future<void> crateApiBindFactoryBindSqlFactoryWChangeMusicDefaultSource(
      {required String musicListName,
      required Int64List ids,
      required List<String> newDefaultSources});

  Future<void> crateApiBindFactoryBindSqlFactoryWChangeMusicInfo(
      {required List<MusicW> musics, required List<MusicInfo> newInfos});

  Future<void> crateApiBindFactoryBindSqlFactoryWChangeMusiclistInfo(
      {required List<MusicListInfo> old, required List<MusicListInfo> new_});

  Future<void> crateApiBindFactoryBindSqlFactoryWCleanUnusedMusicData();

  Future<void> crateApiBindFactoryBindSqlFactoryWCleanUnusedMusiclist();

  Future<void> crateApiBindFactoryBindSqlFactoryWCreateMusiclist(
      {required List<MusicListInfo> musicListInfos});

  Future<void> crateApiBindFactoryBindSqlFactoryWDelDuplicateMusicsOfMusiclist(
      {required MusicListInfo musiclistInfo});

  Future<void> crateApiBindFactoryBindSqlFactoryWDelMusiclist(
      {required List<String> musiclistNames});

  Future<void> crateApiBindFactoryBindSqlFactoryWDelMusics(
      {required String musicListName, required Int64List ids});

  Future<List<MusicListW>> crateApiBindFactoryBindSqlFactoryWGetAllMusiclists();

  Future<List<MusicAggregatorW>> crateApiBindFactoryBindSqlFactoryWGetAllMusics(
      {required MusicListInfo musiclistInfo});

  Future<MusicAggregatorW> crateApiBindFactoryBindSqlFactoryWGetMusicById(
      {required MusicListInfo musicListInfo,
      required PlatformInt64 id,
      required List<String> sources});

  Future<void> crateApiBindFactoryBindSqlFactoryWInitFromPath(
      {required String filepath});

  Future<List<MusicW>> crateApiBindFactoryBindSqlFactoryWReadMusicData(
      {required String source});

  Future<void> crateApiBindFactoryBindSqlFactoryWReorderMusiclist(
      {required Int64List newIds});

  Future<void> crateApiBindFactoryBindSqlFactoryWReorderMusics(
      {required String musicListName, required Int64List newIds});

  Future<void> crateApiBindFactoryBindSqlFactoryWReplaceMusics(
      {required String musicListName,
      required Int64List ids,
      required List<MusicAggregatorW> musics});

  Future<void> crateApiBindFactoryBindSqlFactoryWShutdown();

  Future<void> crateApiBindTypeBindMusicAggregatorWAddMusic(
      {required MusicAggregatorW that, required MusicW music});

  bool crateApiBindTypeBindMusicAggregatorWBelongTo(
      {required MusicAggregatorW that, required MusicW music});

  MusicAggregatorW crateApiBindTypeBindMusicAggregatorWClone(
      {required MusicAggregatorW that});

  Future<(MusicListW, List<MusicAggregatorW>)>
      crateApiBindTypeBindMusicAggregatorWFetchAlbum(
          {required MusicAggregatorW that,
          required int page,
          required int limit});

  Future<String> crateApiBindTypeBindMusicAggregatorWFetchLyric(
      {required MusicAggregatorW that});

  Future<List<MusicW>> crateApiBindTypeBindMusicAggregatorWFetchMusics(
      {required MusicAggregatorW that, required List<String> sources});

  List<MusicW> crateApiBindTypeBindMusicAggregatorWGetAllMusics(
      {required MusicAggregatorW that});

  List<MusicW> crateApiBindTypeBindMusicAggregatorWGetAllMusicsOwned(
      {required MusicAggregatorW that});

  List<String> crateApiBindTypeBindMusicAggregatorWGetAvailableSources(
      {required MusicAggregatorW that});

  MusicW crateApiBindTypeBindMusicAggregatorWGetDefaultMusic(
      {required MusicAggregatorW that});

  String crateApiBindTypeBindMusicAggregatorWGetDefaultSource(
      {required MusicAggregatorW that});

  Future<MusicW?> crateApiBindTypeBindMusicAggregatorWGetMusic(
      {required MusicAggregatorW that, required String source});

  PlatformInt64 crateApiBindTypeBindMusicAggregatorWGetMusicId(
      {required MusicAggregatorW that});

  bool crateApiBindTypeBindMusicAggregatorWMatchFilter(
      {required MusicAggregatorW that, required MusicFuzzFilter filter});

  Future<void> crateApiBindTypeBindMusicAggregatorWSetDefaultSource(
      {required MusicAggregatorW that, required String source});

  String crateApiBindTypeBindMusicAggregatorWToString(
      {required MusicAggregatorW that});

  Future<List<MusicAggregatorW>>
      crateApiBindTypeBindMusicListWFetchAllMusicAggregators(
          {required MusicListW that,
          required int pagesPerBatch,
          required int limit,
          required bool withLyric});

  Future<List<MusicAggregatorW>>
      crateApiBindTypeBindMusicListWGetMusicAggregators(
          {required MusicListW that, required int page, required int limit});

  MusicListInfo crateApiBindTypeBindMusicListWGetMusiclistInfo(
      {required MusicListW that});

  String crateApiBindTypeBindMusicListWSource({required MusicListW that});

  String crateApiBindTypeBindMusicListWToString({required MusicListW that});

  Future<(MusicListW, List<MusicAggregatorW>)>
      crateApiBindTypeBindMusicWFetchAlbum(
          {required MusicW that, required int page, required int limit});

  Future<String> crateApiBindTypeBindMusicWFetchLyric({required MusicW that});

  String crateApiBindTypeBindMusicWGetExtraInfo(
      {required MusicW that, required Quality quality});

  MusicInfo crateApiBindTypeBindMusicWGetMusicInfo({required MusicW that});

  (String, String) crateApiBindTypeBindMusicWGetPrimaryKv(
      {required MusicW that});

  String crateApiBindTypeBindMusicWSource({required MusicW that});

  String crateApiBindTypeBindMusicWToString({required MusicW that});

  Future<String> crateApiCacheFileCacheCacheFile(
      {required String file,
      required String cachePath,
      String? filename,
      String? exportRoot});

  Future<void> crateApiCacheFileCacheDeleteCacheFile(
      {required String file,
      required String cachePath,
      String? filename,
      String? exportRoot});

  Future<String> crateApiCacheFileCacheGenHash({required String str});

  String? crateApiCacheFileCacheUseCacheFile(
      {required String file,
      required String cachePath,
      String? filename,
      String? exportRoot});

  Future<void> crateApiCacheFsUtilCopyDirectory(
      {required String src, required String dst});

  Future<void> crateApiCacheFsUtilCopyFile(
      {required String from, required String to});

  Future<void> crateApiCacheFsUtilRemoveDir({required String dir});

  Future<void> crateApiCacheFsUtilRemoveFile({required String file});

  Future<void> crateApiCacheFsUtilRename(
      {required String from, required String to});

  Future<void> crateApiCacheMusicCacheCacheMusic(
      {required MusicInfo musicInfo, required PlayInfo playinfo});

  Future<void> crateApiCacheMusicCacheDeleteMusicCache(
      {required MusicInfo musicInfo});

  Future<PlayInfo> crateApiCacheMusicCacheGetCachePlayinfo(
      {required MusicInfo musicInfo});

  Future<bool> crateApiCacheMusicCacheHasCachePlayinfo(
      {required MusicInfo musicInfo});

  Future<void> crateApiInitInit();

  Future<Config> crateApiInitInitBackend({required String storeRoot});

  Future<Config> crateApiTypesConfigConfigLoad();

  Future<void> crateApiTypesConfigConfigSave({required Config that});

  Future<ExternApi?> crateApiTypesExternApiExternApiFetchUpdate(
      {required ExternApi that});

  Future<ExternApi> crateApiTypesExternApiExternApiFromPath(
      {required String path});

  Future<ExternApi> crateApiTypesExternApiExternApiFromUrl(
      {required String url});

  Future<Release?> crateApiTypesVersionCheckUpdate(
      {required String currentVersion});

  Future<Release> crateApiTypesVersionGetRelease();

  Future<String> crateApiUtilsCryptoRc4DecryptFromBase64(
      {required String key, required String input});

  Future<String> crateApiUtilsCryptoRc4EncryptToBase64(
      {required String key, required String input});

  Future<String> crateApiUtilsHttpHelperSendRequest(
      {required String method,
      required Map<String, String> headers,
      required String url,
      required String payload});

  Future<String> crateApiUtilsPathUtilUrlEncodeSpecialChars(
      {required String input});

  RustArcIncrementStrongCountFnType
      get rust_arc_increment_strong_count_MusicAggregatorW;

  RustArcDecrementStrongCountFnType
      get rust_arc_decrement_strong_count_MusicAggregatorW;

  CrossPlatformFinalizerArg
      get rust_arc_decrement_strong_count_MusicAggregatorWPtr;

  RustArcIncrementStrongCountFnType
      get rust_arc_increment_strong_count_MusicListW;

  RustArcDecrementStrongCountFnType
      get rust_arc_decrement_strong_count_MusicListW;

  CrossPlatformFinalizerArg get rust_arc_decrement_strong_count_MusicListWPtr;

  RustArcIncrementStrongCountFnType get rust_arc_increment_strong_count_MusicW;

  RustArcDecrementStrongCountFnType get rust_arc_decrement_strong_count_MusicW;

  CrossPlatformFinalizerArg get rust_arc_decrement_strong_count_MusicWPtr;
}

class RustLibApiImpl extends RustLibApiImplPlatform implements RustLibApi {
  RustLibApiImpl({
    required super.handler,
    required super.wire,
    required super.generalizedFrbRustBinding,
    required super.portManager,
  });

  @override
  Future<List<MusicAggregatorW>>
      crateApiBindFactoryBindAggregatorOnlineFactoryWSearchMusicAggregator(
          {required List<MusicAggregatorW> aggregators,
          required List<String> sources,
          required String content,
          required int page,
          required int limit,
          MusicFuzzFilter? filter}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            aggregators, serializer);
        sse_encode_list_String(sources, serializer);
        sse_encode_String(content, serializer);
        sse_encode_u_32(page, serializer);
        sse_encode_u_32(limit, serializer);
        sse_encode_opt_box_autoadd_music_fuzz_filter(filter, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 1, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta:
          kCrateApiBindFactoryBindAggregatorOnlineFactoryWSearchMusicAggregatorConstMeta,
      argValues: [aggregators, sources, content, page, limit, filter],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindFactoryBindAggregatorOnlineFactoryWSearchMusicAggregatorConstMeta =>
          const TaskConstMeta(
            debugName: "aggregator_online_factory_w_search_music_aggregator",
            argNames: [
              "aggregators",
              "sources",
              "content",
              "page",
              "limit",
              "filter"
            ],
          );

  @override
  Future<(MusicListW, List<MusicAggregatorW>)>
      crateApiBindFactoryBindOnlineFactoryWGetMusiclistFromShare(
          {required String shareUrl}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(shareUrl, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 2, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_record_auto_owned_rust_opaque_flutter_rust_bridgefor_generated_rust_auto_opaque_inner_music_list_w_list_auto_owned_rust_opaque_flutter_rust_bridgefor_generated_rust_auto_opaque_inner_music_aggregator_w,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta:
          kCrateApiBindFactoryBindOnlineFactoryWGetMusiclistFromShareConstMeta,
      argValues: [shareUrl],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindFactoryBindOnlineFactoryWGetMusiclistFromShareConstMeta =>
          const TaskConstMeta(
            debugName: "online_factory_w_get_musiclist_from_share",
            argNames: ["shareUrl"],
          );

  @override
  Future<List<MusicListW>> crateApiBindFactoryBindOnlineFactoryWSearchMusiclist(
      {required List<String> sources,
      required String content,
      required int page,
      required int limit}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_list_String(sources, serializer);
        sse_encode_String(content, serializer);
        sse_encode_u_32(page, serializer);
        sse_encode_u_32(limit, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 3, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindFactoryBindOnlineFactoryWSearchMusiclistConstMeta,
      argValues: [sources, content, page, limit],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindFactoryBindOnlineFactoryWSearchMusiclistConstMeta =>
          const TaskConstMeta(
            debugName: "online_factory_w_search_musiclist",
            argNames: ["sources", "content", "page", "limit"],
          );

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWAddMusics(
      {required String musicsListName,
      required List<MusicAggregatorW> musics}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(musicsListName, serializer);
        sse_encode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            musics, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 4, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindFactoryBindSqlFactoryWAddMusicsConstMeta,
      argValues: [musicsListName, musics],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindFactoryBindSqlFactoryWAddMusicsConstMeta =>
      const TaskConstMeta(
        debugName: "sql_factory_w_add_musics",
        argNames: ["musicsListName", "musics"],
      );

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWChangeMusicDefaultSource(
      {required String musicListName,
      required Int64List ids,
      required List<String> newDefaultSources}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(musicListName, serializer);
        sse_encode_list_prim_i_64_strict(ids, serializer);
        sse_encode_list_String(newDefaultSources, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 5, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta:
          kCrateApiBindFactoryBindSqlFactoryWChangeMusicDefaultSourceConstMeta,
      argValues: [musicListName, ids, newDefaultSources],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindFactoryBindSqlFactoryWChangeMusicDefaultSourceConstMeta =>
          const TaskConstMeta(
            debugName: "sql_factory_w_change_music_default_source",
            argNames: ["musicListName", "ids", "newDefaultSources"],
          );

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWChangeMusicInfo(
      {required List<MusicW> musics, required List<MusicInfo> newInfos}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
            musics, serializer);
        sse_encode_list_music_info(newInfos, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 6, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindFactoryBindSqlFactoryWChangeMusicInfoConstMeta,
      argValues: [musics, newInfos],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindFactoryBindSqlFactoryWChangeMusicInfoConstMeta =>
          const TaskConstMeta(
            debugName: "sql_factory_w_change_music_info",
            argNames: ["musics", "newInfos"],
          );

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWChangeMusiclistInfo(
      {required List<MusicListInfo> old, required List<MusicListInfo> new_}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_list_music_list_info(old, serializer);
        sse_encode_list_music_list_info(new_, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 7, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta:
          kCrateApiBindFactoryBindSqlFactoryWChangeMusiclistInfoConstMeta,
      argValues: [old, new_],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindFactoryBindSqlFactoryWChangeMusiclistInfoConstMeta =>
          const TaskConstMeta(
            debugName: "sql_factory_w_change_musiclist_info",
            argNames: ["old", "new_"],
          );

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWCleanUnusedMusicData() {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 8, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta:
          kCrateApiBindFactoryBindSqlFactoryWCleanUnusedMusicDataConstMeta,
      argValues: [],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindFactoryBindSqlFactoryWCleanUnusedMusicDataConstMeta =>
          const TaskConstMeta(
            debugName: "sql_factory_w_clean_unused_music_data",
            argNames: [],
          );

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWCleanUnusedMusiclist() {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 9, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta:
          kCrateApiBindFactoryBindSqlFactoryWCleanUnusedMusiclistConstMeta,
      argValues: [],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindFactoryBindSqlFactoryWCleanUnusedMusiclistConstMeta =>
          const TaskConstMeta(
            debugName: "sql_factory_w_clean_unused_musiclist",
            argNames: [],
          );

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWCreateMusiclist(
      {required List<MusicListInfo> musicListInfos}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_list_music_list_info(musicListInfos, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 10, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindFactoryBindSqlFactoryWCreateMusiclistConstMeta,
      argValues: [musicListInfos],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindFactoryBindSqlFactoryWCreateMusiclistConstMeta =>
          const TaskConstMeta(
            debugName: "sql_factory_w_create_musiclist",
            argNames: ["musicListInfos"],
          );

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWDelDuplicateMusicsOfMusiclist(
      {required MusicListInfo musiclistInfo}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_box_autoadd_music_list_info(musiclistInfo, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 11, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta:
          kCrateApiBindFactoryBindSqlFactoryWDelDuplicateMusicsOfMusiclistConstMeta,
      argValues: [musiclistInfo],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindFactoryBindSqlFactoryWDelDuplicateMusicsOfMusiclistConstMeta =>
          const TaskConstMeta(
            debugName: "sql_factory_w_del_duplicate_musics_of_musiclist",
            argNames: ["musiclistInfo"],
          );

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWDelMusiclist(
      {required List<String> musiclistNames}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_list_String(musiclistNames, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 12, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindFactoryBindSqlFactoryWDelMusiclistConstMeta,
      argValues: [musiclistNames],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindFactoryBindSqlFactoryWDelMusiclistConstMeta =>
      const TaskConstMeta(
        debugName: "sql_factory_w_del_musiclist",
        argNames: ["musiclistNames"],
      );

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWDelMusics(
      {required String musicListName, required Int64List ids}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(musicListName, serializer);
        sse_encode_list_prim_i_64_strict(ids, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 13, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindFactoryBindSqlFactoryWDelMusicsConstMeta,
      argValues: [musicListName, ids],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindFactoryBindSqlFactoryWDelMusicsConstMeta =>
      const TaskConstMeta(
        debugName: "sql_factory_w_del_musics",
        argNames: ["musicListName", "ids"],
      );

  @override
  Future<List<MusicListW>>
      crateApiBindFactoryBindSqlFactoryWGetAllMusiclists() {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 14, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindFactoryBindSqlFactoryWGetAllMusiclistsConstMeta,
      argValues: [],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindFactoryBindSqlFactoryWGetAllMusiclistsConstMeta =>
          const TaskConstMeta(
            debugName: "sql_factory_w_get_all_musiclists",
            argNames: [],
          );

  @override
  Future<List<MusicAggregatorW>> crateApiBindFactoryBindSqlFactoryWGetAllMusics(
      {required MusicListInfo musiclistInfo}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_box_autoadd_music_list_info(musiclistInfo, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 15, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindFactoryBindSqlFactoryWGetAllMusicsConstMeta,
      argValues: [musiclistInfo],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindFactoryBindSqlFactoryWGetAllMusicsConstMeta =>
      const TaskConstMeta(
        debugName: "sql_factory_w_get_all_musics",
        argNames: ["musiclistInfo"],
      );

  @override
  Future<MusicAggregatorW> crateApiBindFactoryBindSqlFactoryWGetMusicById(
      {required MusicListInfo musicListInfo,
      required PlatformInt64 id,
      required List<String> sources}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_box_autoadd_music_list_info(musicListInfo, serializer);
        sse_encode_i_64(id, serializer);
        sse_encode_list_String(sources, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 16, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindFactoryBindSqlFactoryWGetMusicByIdConstMeta,
      argValues: [musicListInfo, id, sources],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindFactoryBindSqlFactoryWGetMusicByIdConstMeta =>
      const TaskConstMeta(
        debugName: "sql_factory_w_get_music_by_id",
        argNames: ["musicListInfo", "id", "sources"],
      );

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWInitFromPath(
      {required String filepath}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(filepath, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 17, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindFactoryBindSqlFactoryWInitFromPathConstMeta,
      argValues: [filepath],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindFactoryBindSqlFactoryWInitFromPathConstMeta =>
      const TaskConstMeta(
        debugName: "sql_factory_w_init_from_path",
        argNames: ["filepath"],
      );

  @override
  Future<List<MusicW>> crateApiBindFactoryBindSqlFactoryWReadMusicData(
      {required String source}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(source, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 18, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindFactoryBindSqlFactoryWReadMusicDataConstMeta,
      argValues: [source],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindFactoryBindSqlFactoryWReadMusicDataConstMeta =>
      const TaskConstMeta(
        debugName: "sql_factory_w_read_music_data",
        argNames: ["source"],
      );

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWReorderMusiclist(
      {required Int64List newIds}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_list_prim_i_64_strict(newIds, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 19, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindFactoryBindSqlFactoryWReorderMusiclistConstMeta,
      argValues: [newIds],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindFactoryBindSqlFactoryWReorderMusiclistConstMeta =>
          const TaskConstMeta(
            debugName: "sql_factory_w_reorder_musiclist",
            argNames: ["newIds"],
          );

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWReorderMusics(
      {required String musicListName, required Int64List newIds}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(musicListName, serializer);
        sse_encode_list_prim_i_64_strict(newIds, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 20, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindFactoryBindSqlFactoryWReorderMusicsConstMeta,
      argValues: [musicListName, newIds],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindFactoryBindSqlFactoryWReorderMusicsConstMeta =>
      const TaskConstMeta(
        debugName: "sql_factory_w_reorder_musics",
        argNames: ["musicListName", "newIds"],
      );

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWReplaceMusics(
      {required String musicListName,
      required Int64List ids,
      required List<MusicAggregatorW> musics}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(musicListName, serializer);
        sse_encode_list_prim_i_64_strict(ids, serializer);
        sse_encode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            musics, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 21, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindFactoryBindSqlFactoryWReplaceMusicsConstMeta,
      argValues: [musicListName, ids, musics],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindFactoryBindSqlFactoryWReplaceMusicsConstMeta =>
      const TaskConstMeta(
        debugName: "sql_factory_w_replace_musics",
        argNames: ["musicListName", "ids", "musics"],
      );

  @override
  Future<void> crateApiBindFactoryBindSqlFactoryWShutdown() {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 22, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindFactoryBindSqlFactoryWShutdownConstMeta,
      argValues: [],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindFactoryBindSqlFactoryWShutdownConstMeta =>
      const TaskConstMeta(
        debugName: "sql_factory_w_shutdown",
        argNames: [],
      );

  @override
  Future<void> crateApiBindTypeBindMusicAggregatorWAddMusic(
      {required MusicAggregatorW that, required MusicW music}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            that, serializer);
        sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
            music, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 23, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindTypeBindMusicAggregatorWAddMusicConstMeta,
      argValues: [that, music],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicAggregatorWAddMusicConstMeta =>
      const TaskConstMeta(
        debugName: "MusicAggregatorW_add_music",
        argNames: ["that", "music"],
      );

  @override
  bool crateApiBindTypeBindMusicAggregatorWBelongTo(
      {required MusicAggregatorW that, required MusicW music}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            that, serializer);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
            music, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 24)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicAggregatorWBelongToConstMeta,
      argValues: [that, music],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicAggregatorWBelongToConstMeta =>
      const TaskConstMeta(
        debugName: "MusicAggregatorW_belong_to",
        argNames: ["that", "music"],
      );

  @override
  MusicAggregatorW crateApiBindTypeBindMusicAggregatorWClone(
      {required MusicAggregatorW that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 25)!;
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicAggregatorWCloneConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicAggregatorWCloneConstMeta =>
      const TaskConstMeta(
        debugName: "MusicAggregatorW_clone",
        argNames: ["that"],
      );

  @override
  Future<(MusicListW, List<MusicAggregatorW>)>
      crateApiBindTypeBindMusicAggregatorWFetchAlbum(
          {required MusicAggregatorW that,
          required int page,
          required int limit}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            that, serializer);
        sse_encode_u_32(page, serializer);
        sse_encode_u_32(limit, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 26, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_record_auto_owned_rust_opaque_flutter_rust_bridgefor_generated_rust_auto_opaque_inner_music_list_w_list_auto_owned_rust_opaque_flutter_rust_bridgefor_generated_rust_auto_opaque_inner_music_aggregator_w,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindTypeBindMusicAggregatorWFetchAlbumConstMeta,
      argValues: [that, page, limit],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicAggregatorWFetchAlbumConstMeta =>
      const TaskConstMeta(
        debugName: "MusicAggregatorW_fetch_album",
        argNames: ["that", "page", "limit"],
      );

  @override
  Future<String> crateApiBindTypeBindMusicAggregatorWFetchLyric(
      {required MusicAggregatorW that}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            that, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 27, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindTypeBindMusicAggregatorWFetchLyricConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicAggregatorWFetchLyricConstMeta =>
      const TaskConstMeta(
        debugName: "MusicAggregatorW_fetch_lyric",
        argNames: ["that"],
      );

  @override
  Future<List<MusicW>> crateApiBindTypeBindMusicAggregatorWFetchMusics(
      {required MusicAggregatorW that, required List<String> sources}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            that, serializer);
        sse_encode_list_String(sources, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 28, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindTypeBindMusicAggregatorWFetchMusicsConstMeta,
      argValues: [that, sources],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicAggregatorWFetchMusicsConstMeta =>
      const TaskConstMeta(
        debugName: "MusicAggregatorW_fetch_musics",
        argNames: ["that", "sources"],
      );

  @override
  List<MusicW> crateApiBindTypeBindMusicAggregatorWGetAllMusics(
      {required MusicAggregatorW that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 29)!;
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicAggregatorWGetAllMusicsConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindTypeBindMusicAggregatorWGetAllMusicsConstMeta =>
          const TaskConstMeta(
            debugName: "MusicAggregatorW_get_all_musics",
            argNames: ["that"],
          );

  @override
  List<MusicW> crateApiBindTypeBindMusicAggregatorWGetAllMusicsOwned(
      {required MusicAggregatorW that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 30)!;
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW,
        decodeErrorData: null,
      ),
      constMeta:
          kCrateApiBindTypeBindMusicAggregatorWGetAllMusicsOwnedConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindTypeBindMusicAggregatorWGetAllMusicsOwnedConstMeta =>
          const TaskConstMeta(
            debugName: "MusicAggregatorW_get_all_musics_owned",
            argNames: ["that"],
          );

  @override
  List<String> crateApiBindTypeBindMusicAggregatorWGetAvailableSources(
      {required MusicAggregatorW that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 31)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_list_String,
        decodeErrorData: null,
      ),
      constMeta:
          kCrateApiBindTypeBindMusicAggregatorWGetAvailableSourcesConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindTypeBindMusicAggregatorWGetAvailableSourcesConstMeta =>
          const TaskConstMeta(
            debugName: "MusicAggregatorW_get_available_sources",
            argNames: ["that"],
          );

  @override
  MusicW crateApiBindTypeBindMusicAggregatorWGetDefaultMusic(
      {required MusicAggregatorW that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 32)!;
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicAggregatorWGetDefaultMusicConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindTypeBindMusicAggregatorWGetDefaultMusicConstMeta =>
          const TaskConstMeta(
            debugName: "MusicAggregatorW_get_default_music",
            argNames: ["that"],
          );

  @override
  String crateApiBindTypeBindMusicAggregatorWGetDefaultSource(
      {required MusicAggregatorW that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 33)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicAggregatorWGetDefaultSourceConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindTypeBindMusicAggregatorWGetDefaultSourceConstMeta =>
          const TaskConstMeta(
            debugName: "MusicAggregatorW_get_default_source",
            argNames: ["that"],
          );

  @override
  Future<MusicW?> crateApiBindTypeBindMusicAggregatorWGetMusic(
      {required MusicAggregatorW that, required String source}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            that, serializer);
        sse_encode_String(source, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 34, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_opt_box_autoadd_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicAggregatorWGetMusicConstMeta,
      argValues: [that, source],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicAggregatorWGetMusicConstMeta =>
      const TaskConstMeta(
        debugName: "MusicAggregatorW_get_music",
        argNames: ["that", "source"],
      );

  @override
  PlatformInt64 crateApiBindTypeBindMusicAggregatorWGetMusicId(
      {required MusicAggregatorW that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 35)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_i_64,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicAggregatorWGetMusicIdConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicAggregatorWGetMusicIdConstMeta =>
      const TaskConstMeta(
        debugName: "MusicAggregatorW_get_music_id",
        argNames: ["that"],
      );

  @override
  bool crateApiBindTypeBindMusicAggregatorWMatchFilter(
      {required MusicAggregatorW that, required MusicFuzzFilter filter}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            that, serializer);
        sse_encode_box_autoadd_music_fuzz_filter(filter, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 36)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicAggregatorWMatchFilterConstMeta,
      argValues: [that, filter],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicAggregatorWMatchFilterConstMeta =>
      const TaskConstMeta(
        debugName: "MusicAggregatorW_match_filter",
        argNames: ["that", "filter"],
      );

  @override
  Future<void> crateApiBindTypeBindMusicAggregatorWSetDefaultSource(
      {required MusicAggregatorW that, required String source}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            that, serializer);
        sse_encode_String(source, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 37, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindTypeBindMusicAggregatorWSetDefaultSourceConstMeta,
      argValues: [that, source],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindTypeBindMusicAggregatorWSetDefaultSourceConstMeta =>
          const TaskConstMeta(
            debugName: "MusicAggregatorW_set_default_source",
            argNames: ["that", "source"],
          );

  @override
  String crateApiBindTypeBindMusicAggregatorWToString(
      {required MusicAggregatorW that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 38)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicAggregatorWToStringConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicAggregatorWToStringConstMeta =>
      const TaskConstMeta(
        debugName: "MusicAggregatorW_to_string",
        argNames: ["that"],
      );

  @override
  Future<List<MusicAggregatorW>>
      crateApiBindTypeBindMusicListWFetchAllMusicAggregators(
          {required MusicListW that,
          required int pagesPerBatch,
          required int limit,
          required bool withLyric}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
            that, serializer);
        sse_encode_u_32(pagesPerBatch, serializer);
        sse_encode_u_32(limit, serializer);
        sse_encode_bool(withLyric, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 39, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta:
          kCrateApiBindTypeBindMusicListWFetchAllMusicAggregatorsConstMeta,
      argValues: [that, pagesPerBatch, limit, withLyric],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindTypeBindMusicListWFetchAllMusicAggregatorsConstMeta =>
          const TaskConstMeta(
            debugName: "MusicListW_fetch_all_music_aggregators",
            argNames: ["that", "pagesPerBatch", "limit", "withLyric"],
          );

  @override
  Future<List<MusicAggregatorW>>
      crateApiBindTypeBindMusicListWGetMusicAggregators(
          {required MusicListW that, required int page, required int limit}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
            that, serializer);
        sse_encode_u_32(page, serializer);
        sse_encode_u_32(limit, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 40, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindTypeBindMusicListWGetMusicAggregatorsConstMeta,
      argValues: [that, page, limit],
      apiImpl: this,
    ));
  }

  TaskConstMeta
      get kCrateApiBindTypeBindMusicListWGetMusicAggregatorsConstMeta =>
          const TaskConstMeta(
            debugName: "MusicListW_get_music_aggregators",
            argNames: ["that", "page", "limit"],
          );

  @override
  MusicListInfo crateApiBindTypeBindMusicListWGetMusiclistInfo(
      {required MusicListW that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 41)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_music_list_info,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicListWGetMusiclistInfoConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicListWGetMusiclistInfoConstMeta =>
      const TaskConstMeta(
        debugName: "MusicListW_get_musiclist_info",
        argNames: ["that"],
      );

  @override
  String crateApiBindTypeBindMusicListWSource({required MusicListW that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 42)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicListWSourceConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicListWSourceConstMeta =>
      const TaskConstMeta(
        debugName: "MusicListW_source",
        argNames: ["that"],
      );

  @override
  String crateApiBindTypeBindMusicListWToString({required MusicListW that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 43)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicListWToStringConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicListWToStringConstMeta =>
      const TaskConstMeta(
        debugName: "MusicListW_to_string",
        argNames: ["that"],
      );

  @override
  Future<(MusicListW, List<MusicAggregatorW>)>
      crateApiBindTypeBindMusicWFetchAlbum(
          {required MusicW that, required int page, required int limit}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
            that, serializer);
        sse_encode_u_32(page, serializer);
        sse_encode_u_32(limit, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 44, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData:
            sse_decode_record_auto_owned_rust_opaque_flutter_rust_bridgefor_generated_rust_auto_opaque_inner_music_list_w_list_auto_owned_rust_opaque_flutter_rust_bridgefor_generated_rust_auto_opaque_inner_music_aggregator_w,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindTypeBindMusicWFetchAlbumConstMeta,
      argValues: [that, page, limit],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicWFetchAlbumConstMeta =>
      const TaskConstMeta(
        debugName: "MusicW_fetch_album",
        argNames: ["that", "page", "limit"],
      );

  @override
  Future<String> crateApiBindTypeBindMusicWFetchLyric({required MusicW that}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
            that, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 45, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiBindTypeBindMusicWFetchLyricConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicWFetchLyricConstMeta =>
      const TaskConstMeta(
        debugName: "MusicW_fetch_lyric",
        argNames: ["that"],
      );

  @override
  String crateApiBindTypeBindMusicWGetExtraInfo(
      {required MusicW that, required Quality quality}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
            that, serializer);
        sse_encode_box_autoadd_quality(quality, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 46)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicWGetExtraInfoConstMeta,
      argValues: [that, quality],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicWGetExtraInfoConstMeta =>
      const TaskConstMeta(
        debugName: "MusicW_get_extra_info",
        argNames: ["that", "quality"],
      );

  @override
  MusicInfo crateApiBindTypeBindMusicWGetMusicInfo({required MusicW that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 47)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_music_info,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicWGetMusicInfoConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicWGetMusicInfoConstMeta =>
      const TaskConstMeta(
        debugName: "MusicW_get_music_info",
        argNames: ["that"],
      );

  @override
  (String, String) crateApiBindTypeBindMusicWGetPrimaryKv(
      {required MusicW that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 48)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_record_string_string,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicWGetPrimaryKvConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicWGetPrimaryKvConstMeta =>
      const TaskConstMeta(
        debugName: "MusicW_get_primary_kv",
        argNames: ["that"],
      );

  @override
  String crateApiBindTypeBindMusicWSource({required MusicW that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 49)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicWSourceConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicWSourceConstMeta =>
      const TaskConstMeta(
        debugName: "MusicW_source",
        argNames: ["that"],
      );

  @override
  String crateApiBindTypeBindMusicWToString({required MusicW that}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
            that, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 50)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiBindTypeBindMusicWToStringConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiBindTypeBindMusicWToStringConstMeta =>
      const TaskConstMeta(
        debugName: "MusicW_to_string",
        argNames: ["that"],
      );

  @override
  Future<String> crateApiCacheFileCacheCacheFile(
      {required String file,
      required String cachePath,
      String? filename,
      String? exportRoot}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(file, serializer);
        sse_encode_String(cachePath, serializer);
        sse_encode_opt_String(filename, serializer);
        sse_encode_opt_String(exportRoot, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 51, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiCacheFileCacheCacheFileConstMeta,
      argValues: [file, cachePath, filename, exportRoot],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiCacheFileCacheCacheFileConstMeta =>
      const TaskConstMeta(
        debugName: "cache_file",
        argNames: ["file", "cachePath", "filename", "exportRoot"],
      );

  @override
  Future<void> crateApiCacheFileCacheDeleteCacheFile(
      {required String file,
      required String cachePath,
      String? filename,
      String? exportRoot}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(file, serializer);
        sse_encode_String(cachePath, serializer);
        sse_encode_opt_String(filename, serializer);
        sse_encode_opt_String(exportRoot, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 52, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiCacheFileCacheDeleteCacheFileConstMeta,
      argValues: [file, cachePath, filename, exportRoot],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiCacheFileCacheDeleteCacheFileConstMeta =>
      const TaskConstMeta(
        debugName: "delete_cache_file",
        argNames: ["file", "cachePath", "filename", "exportRoot"],
      );

  @override
  Future<String> crateApiCacheFileCacheGenHash({required String str}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(str, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 53, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiCacheFileCacheGenHashConstMeta,
      argValues: [str],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiCacheFileCacheGenHashConstMeta =>
      const TaskConstMeta(
        debugName: "gen_hash",
        argNames: ["str"],
      );

  @override
  String? crateApiCacheFileCacheUseCacheFile(
      {required String file,
      required String cachePath,
      String? filename,
      String? exportRoot}) {
    return handler.executeSync(SyncTask(
      callFfi: () {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(file, serializer);
        sse_encode_String(cachePath, serializer);
        sse_encode_opt_String(filename, serializer);
        sse_encode_opt_String(exportRoot, serializer);
        return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 54)!;
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_opt_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiCacheFileCacheUseCacheFileConstMeta,
      argValues: [file, cachePath, filename, exportRoot],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiCacheFileCacheUseCacheFileConstMeta =>
      const TaskConstMeta(
        debugName: "use_cache_file",
        argNames: ["file", "cachePath", "filename", "exportRoot"],
      );

  @override
  Future<void> crateApiCacheFsUtilCopyDirectory(
      {required String src, required String dst}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(src, serializer);
        sse_encode_String(dst, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 55, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiCacheFsUtilCopyDirectoryConstMeta,
      argValues: [src, dst],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiCacheFsUtilCopyDirectoryConstMeta =>
      const TaskConstMeta(
        debugName: "copy_directory",
        argNames: ["src", "dst"],
      );

  @override
  Future<void> crateApiCacheFsUtilCopyFile(
      {required String from, required String to}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(from, serializer);
        sse_encode_String(to, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 56, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiCacheFsUtilCopyFileConstMeta,
      argValues: [from, to],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiCacheFsUtilCopyFileConstMeta =>
      const TaskConstMeta(
        debugName: "copy_file",
        argNames: ["from", "to"],
      );

  @override
  Future<void> crateApiCacheFsUtilRemoveDir({required String dir}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(dir, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 57, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiCacheFsUtilRemoveDirConstMeta,
      argValues: [dir],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiCacheFsUtilRemoveDirConstMeta =>
      const TaskConstMeta(
        debugName: "remove_dir",
        argNames: ["dir"],
      );

  @override
  Future<void> crateApiCacheFsUtilRemoveFile({required String file}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(file, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 58, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiCacheFsUtilRemoveFileConstMeta,
      argValues: [file],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiCacheFsUtilRemoveFileConstMeta =>
      const TaskConstMeta(
        debugName: "remove_file",
        argNames: ["file"],
      );

  @override
  Future<void> crateApiCacheFsUtilRename(
      {required String from, required String to}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(from, serializer);
        sse_encode_String(to, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 59, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiCacheFsUtilRenameConstMeta,
      argValues: [from, to],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiCacheFsUtilRenameConstMeta => const TaskConstMeta(
        debugName: "rename",
        argNames: ["from", "to"],
      );

  @override
  Future<void> crateApiCacheMusicCacheCacheMusic(
      {required MusicInfo musicInfo, required PlayInfo playinfo}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_box_autoadd_music_info(musicInfo, serializer);
        sse_encode_box_autoadd_play_info(playinfo, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 60, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiCacheMusicCacheCacheMusicConstMeta,
      argValues: [musicInfo, playinfo],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiCacheMusicCacheCacheMusicConstMeta =>
      const TaskConstMeta(
        debugName: "cache_music",
        argNames: ["musicInfo", "playinfo"],
      );

  @override
  Future<void> crateApiCacheMusicCacheDeleteMusicCache(
      {required MusicInfo musicInfo}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_box_autoadd_music_info(musicInfo, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 61, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiCacheMusicCacheDeleteMusicCacheConstMeta,
      argValues: [musicInfo],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiCacheMusicCacheDeleteMusicCacheConstMeta =>
      const TaskConstMeta(
        debugName: "delete_music_cache",
        argNames: ["musicInfo"],
      );

  @override
  Future<PlayInfo> crateApiCacheMusicCacheGetCachePlayinfo(
      {required MusicInfo musicInfo}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_box_autoadd_music_info(musicInfo, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 62, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_play_info,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiCacheMusicCacheGetCachePlayinfoConstMeta,
      argValues: [musicInfo],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiCacheMusicCacheGetCachePlayinfoConstMeta =>
      const TaskConstMeta(
        debugName: "get_cache_playinfo",
        argNames: ["musicInfo"],
      );

  @override
  Future<bool> crateApiCacheMusicCacheHasCachePlayinfo(
      {required MusicInfo musicInfo}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_box_autoadd_music_info(musicInfo, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 63, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_bool,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiCacheMusicCacheHasCachePlayinfoConstMeta,
      argValues: [musicInfo],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiCacheMusicCacheHasCachePlayinfoConstMeta =>
      const TaskConstMeta(
        debugName: "has_cache_playinfo",
        argNames: ["musicInfo"],
      );

  @override
  Future<void> crateApiInitInit() {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 64, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiInitInitConstMeta,
      argValues: [],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiInitInitConstMeta => const TaskConstMeta(
        debugName: "init",
        argNames: [],
      );

  @override
  Future<Config> crateApiInitInitBackend({required String storeRoot}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(storeRoot, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 65, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_config,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiInitInitBackendConstMeta,
      argValues: [storeRoot],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiInitInitBackendConstMeta => const TaskConstMeta(
        debugName: "init_backend",
        argNames: ["storeRoot"],
      );

  @override
  Future<Config> crateApiTypesConfigConfigLoad() {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 66, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_config,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiTypesConfigConfigLoadConstMeta,
      argValues: [],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiTypesConfigConfigLoadConstMeta =>
      const TaskConstMeta(
        debugName: "config_load",
        argNames: [],
      );

  @override
  Future<void> crateApiTypesConfigConfigSave({required Config that}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_box_autoadd_config(that, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 67, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_unit,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiTypesConfigConfigSaveConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiTypesConfigConfigSaveConstMeta =>
      const TaskConstMeta(
        debugName: "config_save",
        argNames: ["that"],
      );

  @override
  Future<ExternApi?> crateApiTypesExternApiExternApiFetchUpdate(
      {required ExternApi that}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_box_autoadd_extern_api(that, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 68, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_opt_box_autoadd_extern_api,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiTypesExternApiExternApiFetchUpdateConstMeta,
      argValues: [that],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiTypesExternApiExternApiFetchUpdateConstMeta =>
      const TaskConstMeta(
        debugName: "extern_api_fetch_update",
        argNames: ["that"],
      );

  @override
  Future<ExternApi> crateApiTypesExternApiExternApiFromPath(
      {required String path}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(path, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 69, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_extern_api,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiTypesExternApiExternApiFromPathConstMeta,
      argValues: [path],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiTypesExternApiExternApiFromPathConstMeta =>
      const TaskConstMeta(
        debugName: "extern_api_from_path",
        argNames: ["path"],
      );

  @override
  Future<ExternApi> crateApiTypesExternApiExternApiFromUrl(
      {required String url}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(url, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 70, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_extern_api,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiTypesExternApiExternApiFromUrlConstMeta,
      argValues: [url],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiTypesExternApiExternApiFromUrlConstMeta =>
      const TaskConstMeta(
        debugName: "extern_api_from_url",
        argNames: ["url"],
      );

  @override
  Future<Release?> crateApiTypesVersionCheckUpdate(
      {required String currentVersion}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(currentVersion, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 71, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_opt_box_autoadd_release,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiTypesVersionCheckUpdateConstMeta,
      argValues: [currentVersion],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiTypesVersionCheckUpdateConstMeta =>
      const TaskConstMeta(
        debugName: "check_update",
        argNames: ["currentVersion"],
      );

  @override
  Future<Release> crateApiTypesVersionGetRelease() {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 72, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_release,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiTypesVersionGetReleaseConstMeta,
      argValues: [],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiTypesVersionGetReleaseConstMeta =>
      const TaskConstMeta(
        debugName: "get_release",
        argNames: [],
      );

  @override
  Future<String> crateApiUtilsCryptoRc4DecryptFromBase64(
      {required String key, required String input}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(key, serializer);
        sse_encode_String(input, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 73, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiUtilsCryptoRc4DecryptFromBase64ConstMeta,
      argValues: [key, input],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiUtilsCryptoRc4DecryptFromBase64ConstMeta =>
      const TaskConstMeta(
        debugName: "rc4_decrypt_from_base64",
        argNames: ["key", "input"],
      );

  @override
  Future<String> crateApiUtilsCryptoRc4EncryptToBase64(
      {required String key, required String input}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(key, serializer);
        sse_encode_String(input, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 74, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: sse_decode_AnyhowException,
      ),
      constMeta: kCrateApiUtilsCryptoRc4EncryptToBase64ConstMeta,
      argValues: [key, input],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiUtilsCryptoRc4EncryptToBase64ConstMeta =>
      const TaskConstMeta(
        debugName: "rc4_encrypt_to_base64",
        argNames: ["key", "input"],
      );

  @override
  Future<String> crateApiUtilsHttpHelperSendRequest(
      {required String method,
      required Map<String, String> headers,
      required String url,
      required String payload}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(method, serializer);
        sse_encode_Map_String_String(headers, serializer);
        sse_encode_String(url, serializer);
        sse_encode_String(payload, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 75, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiUtilsHttpHelperSendRequestConstMeta,
      argValues: [method, headers, url, payload],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiUtilsHttpHelperSendRequestConstMeta =>
      const TaskConstMeta(
        debugName: "send_request",
        argNames: ["method", "headers", "url", "payload"],
      );

  @override
  Future<String> crateApiUtilsPathUtilUrlEncodeSpecialChars(
      {required String input}) {
    return handler.executeNormal(NormalTask(
      callFfi: (port_) {
        final serializer = SseSerializer(generalizedFrbRustBinding);
        sse_encode_String(input, serializer);
        pdeCallFfi(generalizedFrbRustBinding, serializer,
            funcId: 76, port: port_);
      },
      codec: SseCodec(
        decodeSuccessData: sse_decode_String,
        decodeErrorData: null,
      ),
      constMeta: kCrateApiUtilsPathUtilUrlEncodeSpecialCharsConstMeta,
      argValues: [input],
      apiImpl: this,
    ));
  }

  TaskConstMeta get kCrateApiUtilsPathUtilUrlEncodeSpecialCharsConstMeta =>
      const TaskConstMeta(
        debugName: "url_encode_special_chars",
        argNames: ["input"],
      );

  RustArcIncrementStrongCountFnType
      get rust_arc_increment_strong_count_MusicAggregatorW => wire
          .rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW;

  RustArcDecrementStrongCountFnType
      get rust_arc_decrement_strong_count_MusicAggregatorW => wire
          .rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW;

  RustArcIncrementStrongCountFnType
      get rust_arc_increment_strong_count_MusicListW => wire
          .rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW;

  RustArcDecrementStrongCountFnType
      get rust_arc_decrement_strong_count_MusicListW => wire
          .rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW;

  RustArcIncrementStrongCountFnType
      get rust_arc_increment_strong_count_MusicW => wire
          .rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW;

  RustArcDecrementStrongCountFnType
      get rust_arc_decrement_strong_count_MusicW => wire
          .rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW;

  @protected
  AnyhowException dco_decode_AnyhowException(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return AnyhowException(raw as String);
  }

  @protected
  MusicAggregatorW
      dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return MusicAggregatorWImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  MusicListW
      dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return MusicListWImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  MusicW
      dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return MusicWImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  MusicAggregatorW
      dco_decode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return MusicAggregatorWImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  MusicAggregatorW
      dco_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return MusicAggregatorWImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  MusicListW
      dco_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return MusicListWImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  MusicW
      dco_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return MusicWImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  DateTime dco_decode_Chrono_Utc(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dcoDecodeTimestamp(ts: dco_decode_i_64(raw).toInt(), isUtc: true);
  }

  @protected
  Map<String, String> dco_decode_Map_String_String(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return Map.fromEntries(dco_decode_list_record_string_string(raw)
        .map((e) => MapEntry(e.$1, e.$2)));
  }

  @protected
  MusicAggregatorW
      dco_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return MusicAggregatorWImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  MusicListW
      dco_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return MusicListWImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  MusicW
      dco_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return MusicWImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  String dco_decode_String(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as String;
  }

  @protected
  AggregatorOnlineFactoryW dco_decode_aggregator_online_factory_w(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.isNotEmpty)
      throw Exception('unexpected arr length: expect 0 but see ${arr.length}');
    return const AggregatorOnlineFactoryW();
  }

  @protected
  Asset dco_decode_asset(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 13)
      throw Exception('unexpected arr length: expect 13 but see ${arr.length}');
    return Asset(
      url: dco_decode_String(arr[0]),
      id: dco_decode_u_64(arr[1]),
      nodeId: dco_decode_String(arr[2]),
      name: dco_decode_String(arr[3]),
      label: dco_decode_opt_String(arr[4]),
      uploader: dco_decode_author(arr[5]),
      contentType: dco_decode_String(arr[6]),
      state: dco_decode_String(arr[7]),
      size: dco_decode_u_64(arr[8]),
      downloadCount: dco_decode_u_64(arr[9]),
      createdAt: dco_decode_String(arr[10]),
      updatedAt: dco_decode_String(arr[11]),
      browserDownloadUrl: dco_decode_String(arr[12]),
    );
  }

  @protected
  Author dco_decode_author(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 18)
      throw Exception('unexpected arr length: expect 18 but see ${arr.length}');
    return Author(
      login: dco_decode_String(arr[0]),
      id: dco_decode_u_64(arr[1]),
      nodeId: dco_decode_String(arr[2]),
      avatarUrl: dco_decode_String(arr[3]),
      gravatarId: dco_decode_String(arr[4]),
      url: dco_decode_String(arr[5]),
      htmlUrl: dco_decode_String(arr[6]),
      followersUrl: dco_decode_String(arr[7]),
      followingUrl: dco_decode_String(arr[8]),
      gistsUrl: dco_decode_String(arr[9]),
      starredUrl: dco_decode_String(arr[10]),
      subscriptionsUrl: dco_decode_String(arr[11]),
      organizationsUrl: dco_decode_String(arr[12]),
      reposUrl: dco_decode_String(arr[13]),
      eventsUrl: dco_decode_String(arr[14]),
      receivedEventsUrl: dco_decode_String(arr[15]),
      type: dco_decode_String(arr[16]),
      siteAdmin: dco_decode_bool(arr[17]),
    );
  }

  @protected
  bool dco_decode_bool(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as bool;
  }

  @protected
  MusicW
      dco_decode_box_autoadd_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
        raw);
  }

  @protected
  DateTime dco_decode_box_autoadd_Chrono_Utc(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_Chrono_Utc(raw);
  }

  @protected
  Config dco_decode_box_autoadd_config(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_config(raw);
  }

  @protected
  ExternApi dco_decode_box_autoadd_extern_api(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_extern_api(raw);
  }

  @protected
  ExtraInfo dco_decode_box_autoadd_extra_info(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_extra_info(raw);
  }

  @protected
  MusicFuzzFilter dco_decode_box_autoadd_music_fuzz_filter(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_music_fuzz_filter(raw);
  }

  @protected
  MusicInfo dco_decode_box_autoadd_music_info(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_music_info(raw);
  }

  @protected
  MusicListInfo dco_decode_box_autoadd_music_list_info(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_music_list_info(raw);
  }

  @protected
  PlayInfo dco_decode_box_autoadd_play_info(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_play_info(raw);
  }

  @protected
  Quality dco_decode_box_autoadd_quality(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_quality(raw);
  }

  @protected
  Release dco_decode_box_autoadd_release(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dco_decode_release(raw);
  }

  @protected
  int dco_decode_box_autoadd_u_32(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as int;
  }

  @protected
  Config dco_decode_config(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 11)
      throw Exception('unexpected arr length: expect 11 but see ${arr.length}');
    return Config(
      userAgreement: dco_decode_bool(arr[0]),
      externApi: dco_decode_opt_box_autoadd_extern_api(arr[1]),
      versionAutoUpdate: dco_decode_bool(arr[2]),
      externApiAutoUpdate: dco_decode_bool(arr[3]),
      wifiAutoQuality: dco_decode_String(arr[4]),
      mobileAutoQuality: dco_decode_String(arr[5]),
      savePicWhenAddMusicList: dco_decode_bool(arr[6]),
      saveLyricWhenAddMusicList: dco_decode_bool(arr[7]),
      exportCacheRoot: dco_decode_opt_String(arr[8]),
      lastExportCacheRoot: dco_decode_opt_String(arr[9]),
      externApiPath: dco_decode_opt_String(arr[10]),
    );
  }

  @protected
  ExternApi dco_decode_extern_api(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 4)
      throw Exception('unexpected arr length: expect 4 but see ${arr.length}');
    return ExternApi(
      url: dco_decode_opt_String(arr[0]),
      localPath: dco_decode_String(arr[1]),
      lastHash: dco_decode_opt_String(arr[2]),
      lastModifiedTime: dco_decode_opt_box_autoadd_Chrono_Utc(arr[3]),
    );
  }

  @protected
  ExtraInfo dco_decode_extra_info(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 2)
      throw Exception('unexpected arr length: expect 2 but see ${arr.length}');
    return ExtraInfo(
      playCount: dco_decode_opt_box_autoadd_u_32(arr[0]),
      musicCount: dco_decode_opt_box_autoadd_u_32(arr[1]),
    );
  }

  @protected
  PlatformInt64 dco_decode_i_64(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dcoDecodeI64(raw);
  }

  @protected
  List<MusicAggregatorW>
      dco_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>)
        .map(
            dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW)
        .toList();
  }

  @protected
  List<MusicListW>
      dco_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>)
        .map(
            dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW)
        .toList();
  }

  @protected
  List<MusicW>
      dco_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>)
        .map(
            dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW)
        .toList();
  }

  @protected
  List<String> dco_decode_list_String(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>).map(dco_decode_String).toList();
  }

  @protected
  List<Asset> dco_decode_list_asset(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>).map(dco_decode_asset).toList();
  }

  @protected
  List<MusicInfo> dco_decode_list_music_info(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>).map(dco_decode_music_info).toList();
  }

  @protected
  List<MusicListInfo> dco_decode_list_music_list_info(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>).map(dco_decode_music_list_info).toList();
  }

  @protected
  Int64List dco_decode_list_prim_i_64_strict(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dcoDecodeInt64List(raw);
  }

  @protected
  Uint8List dco_decode_list_prim_u_8_strict(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as Uint8List;
  }

  @protected
  List<Quality> dco_decode_list_quality(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>).map(dco_decode_quality).toList();
  }

  @protected
  List<(String, String)> dco_decode_list_record_string_string(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return (raw as List<dynamic>).map(dco_decode_record_string_string).toList();
  }

  @protected
  MusicFuzzFilter dco_decode_music_fuzz_filter(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 3)
      throw Exception('unexpected arr length: expect 3 but see ${arr.length}');
    return MusicFuzzFilter(
      name: dco_decode_opt_String(arr[0]),
      artist: dco_decode_list_String(arr[1]),
      album: dco_decode_opt_String(arr[2]),
    );
  }

  @protected
  MusicInfo dco_decode_music_info(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 10)
      throw Exception('unexpected arr length: expect 10 but see ${arr.length}');
    return MusicInfo(
      id: dco_decode_i_64(arr[0]),
      source: dco_decode_String(arr[1]),
      name: dco_decode_String(arr[2]),
      artist: dco_decode_list_String(arr[3]),
      duration: dco_decode_opt_box_autoadd_u_32(arr[4]),
      album: dco_decode_opt_String(arr[5]),
      qualities: dco_decode_list_quality(arr[6]),
      defaultQuality: dco_decode_opt_box_autoadd_quality(arr[7]),
      artPic: dco_decode_opt_String(arr[8]),
      lyric: dco_decode_opt_String(arr[9]),
    );
  }

  @protected
  MusicListInfo dco_decode_music_list_info(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 5)
      throw Exception('unexpected arr length: expect 5 but see ${arr.length}');
    return MusicListInfo(
      id: dco_decode_i_64(arr[0]),
      name: dco_decode_String(arr[1]),
      artPic: dco_decode_String(arr[2]),
      desc: dco_decode_String(arr[3]),
      extra: dco_decode_opt_box_autoadd_extra_info(arr[4]),
    );
  }

  @protected
  OnlineFactoryW dco_decode_online_factory_w(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.isNotEmpty)
      throw Exception('unexpected arr length: expect 0 but see ${arr.length}');
    return const OnlineFactoryW();
  }

  @protected
  String? dco_decode_opt_String(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw == null ? null : dco_decode_String(raw);
  }

  @protected
  MusicW?
      dco_decode_opt_box_autoadd_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw == null
        ? null
        : dco_decode_box_autoadd_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
            raw);
  }

  @protected
  DateTime? dco_decode_opt_box_autoadd_Chrono_Utc(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw == null ? null : dco_decode_box_autoadd_Chrono_Utc(raw);
  }

  @protected
  ExternApi? dco_decode_opt_box_autoadd_extern_api(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw == null ? null : dco_decode_box_autoadd_extern_api(raw);
  }

  @protected
  ExtraInfo? dco_decode_opt_box_autoadd_extra_info(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw == null ? null : dco_decode_box_autoadd_extra_info(raw);
  }

  @protected
  MusicFuzzFilter? dco_decode_opt_box_autoadd_music_fuzz_filter(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw == null ? null : dco_decode_box_autoadd_music_fuzz_filter(raw);
  }

  @protected
  Quality? dco_decode_opt_box_autoadd_quality(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw == null ? null : dco_decode_box_autoadd_quality(raw);
  }

  @protected
  Release? dco_decode_opt_box_autoadd_release(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw == null ? null : dco_decode_box_autoadd_release(raw);
  }

  @protected
  int? dco_decode_opt_box_autoadd_u_32(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw == null ? null : dco_decode_box_autoadd_u_32(raw);
  }

  @protected
  PlayInfo dco_decode_play_info(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 2)
      throw Exception('unexpected arr length: expect 2 but see ${arr.length}');
    return PlayInfo(
      uri: dco_decode_String(arr[0]),
      quality: dco_decode_quality(arr[1]),
    );
  }

  @protected
  Quality dco_decode_quality(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 5)
      throw Exception('unexpected arr length: expect 5 but see ${arr.length}');
    return Quality(
      short: dco_decode_String(arr[0]),
      level: dco_decode_opt_String(arr[1]),
      bitrate: dco_decode_opt_box_autoadd_u_32(arr[2]),
      format: dco_decode_opt_String(arr[3]),
      size: dco_decode_opt_String(arr[4]),
    );
  }

  @protected
  (
    MusicListW,
    List<MusicAggregatorW>
  ) dco_decode_record_auto_owned_rust_opaque_flutter_rust_bridgefor_generated_rust_auto_opaque_inner_music_list_w_list_auto_owned_rust_opaque_flutter_rust_bridgefor_generated_rust_auto_opaque_inner_music_aggregator_w(
      dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 2) {
      throw Exception('Expected 2 elements, got ${arr.length}');
    }
    return (
      dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
          arr[0]),
      dco_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          arr[1]),
    );
  }

  @protected
  (String, String) dco_decode_record_string_string(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 2) {
      throw Exception('Expected 2 elements, got ${arr.length}');
    }
    return (
      dco_decode_String(arr[0]),
      dco_decode_String(arr[1]),
    );
  }

  @protected
  Release dco_decode_release(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.length != 18)
      throw Exception('unexpected arr length: expect 18 but see ${arr.length}');
    return Release(
      url: dco_decode_String(arr[0]),
      assetsUrl: dco_decode_String(arr[1]),
      uploadUrl: dco_decode_String(arr[2]),
      htmlUrl: dco_decode_String(arr[3]),
      id: dco_decode_u_64(arr[4]),
      author: dco_decode_author(arr[5]),
      nodeId: dco_decode_String(arr[6]),
      tagName: dco_decode_String(arr[7]),
      targetCommitish: dco_decode_String(arr[8]),
      name: dco_decode_String(arr[9]),
      draft: dco_decode_bool(arr[10]),
      prerelease: dco_decode_bool(arr[11]),
      createdAt: dco_decode_String(arr[12]),
      publishedAt: dco_decode_String(arr[13]),
      assets: dco_decode_list_asset(arr[14]),
      tarballUrl: dco_decode_String(arr[15]),
      zipballUrl: dco_decode_String(arr[16]),
      body: dco_decode_String(arr[17]),
    );
  }

  @protected
  SqlFactoryW dco_decode_sql_factory_w(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    final arr = raw as List<dynamic>;
    if (arr.isNotEmpty)
      throw Exception('unexpected arr length: expect 0 but see ${arr.length}');
    return const SqlFactoryW();
  }

  @protected
  int dco_decode_u_32(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as int;
  }

  @protected
  BigInt dco_decode_u_64(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dcoDecodeU64(raw);
  }

  @protected
  int dco_decode_u_8(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as int;
  }

  @protected
  void dco_decode_unit(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return;
  }

  @protected
  BigInt dco_decode_usize(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dcoDecodeU64(raw);
  }

  @protected
  AnyhowException sse_decode_AnyhowException(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_String(deserializer);
    return AnyhowException(inner);
  }

  @protected
  MusicAggregatorW
      sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return MusicAggregatorWImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  MusicListW
      sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return MusicListWImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  MusicW
      sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return MusicWImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  MusicAggregatorW
      sse_decode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return MusicAggregatorWImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  MusicAggregatorW
      sse_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return MusicAggregatorWImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  MusicListW
      sse_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return MusicListWImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  MusicW
      sse_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return MusicWImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  DateTime sse_decode_Chrono_Utc(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_i_64(deserializer);
    return DateTime.fromMicrosecondsSinceEpoch(inner.toInt(), isUtc: true);
  }

  @protected
  Map<String, String> sse_decode_Map_String_String(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_list_record_string_string(deserializer);
    return Map.fromEntries(inner.map((e) => MapEntry(e.$1, e.$2)));
  }

  @protected
  MusicAggregatorW
      sse_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return MusicAggregatorWImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  MusicListW
      sse_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return MusicListWImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  MusicW
      sse_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return MusicWImpl.frbInternalSseDecode(
        sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  String sse_decode_String(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_list_prim_u_8_strict(deserializer);
    return utf8.decoder.convert(inner);
  }

  @protected
  AggregatorOnlineFactoryW sse_decode_aggregator_online_factory_w(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return const AggregatorOnlineFactoryW();
  }

  @protected
  Asset sse_decode_asset(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_url = sse_decode_String(deserializer);
    var var_id = sse_decode_u_64(deserializer);
    var var_nodeId = sse_decode_String(deserializer);
    var var_name = sse_decode_String(deserializer);
    var var_label = sse_decode_opt_String(deserializer);
    var var_uploader = sse_decode_author(deserializer);
    var var_contentType = sse_decode_String(deserializer);
    var var_state = sse_decode_String(deserializer);
    var var_size = sse_decode_u_64(deserializer);
    var var_downloadCount = sse_decode_u_64(deserializer);
    var var_createdAt = sse_decode_String(deserializer);
    var var_updatedAt = sse_decode_String(deserializer);
    var var_browserDownloadUrl = sse_decode_String(deserializer);
    return Asset(
        url: var_url,
        id: var_id,
        nodeId: var_nodeId,
        name: var_name,
        label: var_label,
        uploader: var_uploader,
        contentType: var_contentType,
        state: var_state,
        size: var_size,
        downloadCount: var_downloadCount,
        createdAt: var_createdAt,
        updatedAt: var_updatedAt,
        browserDownloadUrl: var_browserDownloadUrl);
  }

  @protected
  Author sse_decode_author(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_login = sse_decode_String(deserializer);
    var var_id = sse_decode_u_64(deserializer);
    var var_nodeId = sse_decode_String(deserializer);
    var var_avatarUrl = sse_decode_String(deserializer);
    var var_gravatarId = sse_decode_String(deserializer);
    var var_url = sse_decode_String(deserializer);
    var var_htmlUrl = sse_decode_String(deserializer);
    var var_followersUrl = sse_decode_String(deserializer);
    var var_followingUrl = sse_decode_String(deserializer);
    var var_gistsUrl = sse_decode_String(deserializer);
    var var_starredUrl = sse_decode_String(deserializer);
    var var_subscriptionsUrl = sse_decode_String(deserializer);
    var var_organizationsUrl = sse_decode_String(deserializer);
    var var_reposUrl = sse_decode_String(deserializer);
    var var_eventsUrl = sse_decode_String(deserializer);
    var var_receivedEventsUrl = sse_decode_String(deserializer);
    var var_type = sse_decode_String(deserializer);
    var var_siteAdmin = sse_decode_bool(deserializer);
    return Author(
        login: var_login,
        id: var_id,
        nodeId: var_nodeId,
        avatarUrl: var_avatarUrl,
        gravatarId: var_gravatarId,
        url: var_url,
        htmlUrl: var_htmlUrl,
        followersUrl: var_followersUrl,
        followingUrl: var_followingUrl,
        gistsUrl: var_gistsUrl,
        starredUrl: var_starredUrl,
        subscriptionsUrl: var_subscriptionsUrl,
        organizationsUrl: var_organizationsUrl,
        reposUrl: var_reposUrl,
        eventsUrl: var_eventsUrl,
        receivedEventsUrl: var_receivedEventsUrl,
        type: var_type,
        siteAdmin: var_siteAdmin);
  }

  @protected
  bool sse_decode_bool(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getUint8() != 0;
  }

  @protected
  MusicW
      sse_decode_box_autoadd_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
        deserializer));
  }

  @protected
  DateTime sse_decode_box_autoadd_Chrono_Utc(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_Chrono_Utc(deserializer));
  }

  @protected
  Config sse_decode_box_autoadd_config(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_config(deserializer));
  }

  @protected
  ExternApi sse_decode_box_autoadd_extern_api(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_extern_api(deserializer));
  }

  @protected
  ExtraInfo sse_decode_box_autoadd_extra_info(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_extra_info(deserializer));
  }

  @protected
  MusicFuzzFilter sse_decode_box_autoadd_music_fuzz_filter(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_music_fuzz_filter(deserializer));
  }

  @protected
  MusicInfo sse_decode_box_autoadd_music_info(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_music_info(deserializer));
  }

  @protected
  MusicListInfo sse_decode_box_autoadd_music_list_info(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_music_list_info(deserializer));
  }

  @protected
  PlayInfo sse_decode_box_autoadd_play_info(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_play_info(deserializer));
  }

  @protected
  Quality sse_decode_box_autoadd_quality(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_quality(deserializer));
  }

  @protected
  Release sse_decode_box_autoadd_release(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_release(deserializer));
  }

  @protected
  int sse_decode_box_autoadd_u_32(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return (sse_decode_u_32(deserializer));
  }

  @protected
  Config sse_decode_config(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_userAgreement = sse_decode_bool(deserializer);
    var var_externApi = sse_decode_opt_box_autoadd_extern_api(deserializer);
    var var_versionAutoUpdate = sse_decode_bool(deserializer);
    var var_externApiAutoUpdate = sse_decode_bool(deserializer);
    var var_wifiAutoQuality = sse_decode_String(deserializer);
    var var_mobileAutoQuality = sse_decode_String(deserializer);
    var var_savePicWhenAddMusicList = sse_decode_bool(deserializer);
    var var_saveLyricWhenAddMusicList = sse_decode_bool(deserializer);
    var var_exportCacheRoot = sse_decode_opt_String(deserializer);
    var var_lastExportCacheRoot = sse_decode_opt_String(deserializer);
    var var_externApiPath = sse_decode_opt_String(deserializer);
    return Config(
        userAgreement: var_userAgreement,
        externApi: var_externApi,
        versionAutoUpdate: var_versionAutoUpdate,
        externApiAutoUpdate: var_externApiAutoUpdate,
        wifiAutoQuality: var_wifiAutoQuality,
        mobileAutoQuality: var_mobileAutoQuality,
        savePicWhenAddMusicList: var_savePicWhenAddMusicList,
        saveLyricWhenAddMusicList: var_saveLyricWhenAddMusicList,
        exportCacheRoot: var_exportCacheRoot,
        lastExportCacheRoot: var_lastExportCacheRoot,
        externApiPath: var_externApiPath);
  }

  @protected
  ExternApi sse_decode_extern_api(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_url = sse_decode_opt_String(deserializer);
    var var_localPath = sse_decode_String(deserializer);
    var var_lastHash = sse_decode_opt_String(deserializer);
    var var_lastModifiedTime =
        sse_decode_opt_box_autoadd_Chrono_Utc(deserializer);
    return ExternApi(
        url: var_url,
        localPath: var_localPath,
        lastHash: var_lastHash,
        lastModifiedTime: var_lastModifiedTime);
  }

  @protected
  ExtraInfo sse_decode_extra_info(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_playCount = sse_decode_opt_box_autoadd_u_32(deserializer);
    var var_musicCount = sse_decode_opt_box_autoadd_u_32(deserializer);
    return ExtraInfo(playCount: var_playCount, musicCount: var_musicCount);
  }

  @protected
  PlatformInt64 sse_decode_i_64(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getPlatformInt64();
  }

  @protected
  List<MusicAggregatorW>
      sse_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <MusicAggregatorW>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(
          sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
              deserializer));
    }
    return ans_;
  }

  @protected
  List<MusicListW>
      sse_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <MusicListW>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(
          sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
              deserializer));
    }
    return ans_;
  }

  @protected
  List<MusicW>
      sse_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <MusicW>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(
          sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
              deserializer));
    }
    return ans_;
  }

  @protected
  List<String> sse_decode_list_String(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <String>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(sse_decode_String(deserializer));
    }
    return ans_;
  }

  @protected
  List<Asset> sse_decode_list_asset(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <Asset>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(sse_decode_asset(deserializer));
    }
    return ans_;
  }

  @protected
  List<MusicInfo> sse_decode_list_music_info(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <MusicInfo>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(sse_decode_music_info(deserializer));
    }
    return ans_;
  }

  @protected
  List<MusicListInfo> sse_decode_list_music_list_info(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <MusicListInfo>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(sse_decode_music_list_info(deserializer));
    }
    return ans_;
  }

  @protected
  Int64List sse_decode_list_prim_i_64_strict(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var len_ = sse_decode_i_32(deserializer);
    return deserializer.buffer.getInt64List(len_);
  }

  @protected
  Uint8List sse_decode_list_prim_u_8_strict(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var len_ = sse_decode_i_32(deserializer);
    return deserializer.buffer.getUint8List(len_);
  }

  @protected
  List<Quality> sse_decode_list_quality(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <Quality>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(sse_decode_quality(deserializer));
    }
    return ans_;
  }

  @protected
  List<(String, String)> sse_decode_list_record_string_string(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    var len_ = sse_decode_i_32(deserializer);
    var ans_ = <(String, String)>[];
    for (var idx_ = 0; idx_ < len_; ++idx_) {
      ans_.add(sse_decode_record_string_string(deserializer));
    }
    return ans_;
  }

  @protected
  MusicFuzzFilter sse_decode_music_fuzz_filter(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_name = sse_decode_opt_String(deserializer);
    var var_artist = sse_decode_list_String(deserializer);
    var var_album = sse_decode_opt_String(deserializer);
    return MusicFuzzFilter(
        name: var_name, artist: var_artist, album: var_album);
  }

  @protected
  MusicInfo sse_decode_music_info(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_id = sse_decode_i_64(deserializer);
    var var_source = sse_decode_String(deserializer);
    var var_name = sse_decode_String(deserializer);
    var var_artist = sse_decode_list_String(deserializer);
    var var_duration = sse_decode_opt_box_autoadd_u_32(deserializer);
    var var_album = sse_decode_opt_String(deserializer);
    var var_qualities = sse_decode_list_quality(deserializer);
    var var_defaultQuality = sse_decode_opt_box_autoadd_quality(deserializer);
    var var_artPic = sse_decode_opt_String(deserializer);
    var var_lyric = sse_decode_opt_String(deserializer);
    return MusicInfo(
        id: var_id,
        source: var_source,
        name: var_name,
        artist: var_artist,
        duration: var_duration,
        album: var_album,
        qualities: var_qualities,
        defaultQuality: var_defaultQuality,
        artPic: var_artPic,
        lyric: var_lyric);
  }

  @protected
  MusicListInfo sse_decode_music_list_info(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_id = sse_decode_i_64(deserializer);
    var var_name = sse_decode_String(deserializer);
    var var_artPic = sse_decode_String(deserializer);
    var var_desc = sse_decode_String(deserializer);
    var var_extra = sse_decode_opt_box_autoadd_extra_info(deserializer);
    return MusicListInfo(
        id: var_id,
        name: var_name,
        artPic: var_artPic,
        desc: var_desc,
        extra: var_extra);
  }

  @protected
  OnlineFactoryW sse_decode_online_factory_w(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return const OnlineFactoryW();
  }

  @protected
  String? sse_decode_opt_String(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    if (sse_decode_bool(deserializer)) {
      return (sse_decode_String(deserializer));
    } else {
      return null;
    }
  }

  @protected
  MusicW?
      sse_decode_opt_box_autoadd_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    if (sse_decode_bool(deserializer)) {
      return (sse_decode_box_autoadd_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          deserializer));
    } else {
      return null;
    }
  }

  @protected
  DateTime? sse_decode_opt_box_autoadd_Chrono_Utc(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    if (sse_decode_bool(deserializer)) {
      return (sse_decode_box_autoadd_Chrono_Utc(deserializer));
    } else {
      return null;
    }
  }

  @protected
  ExternApi? sse_decode_opt_box_autoadd_extern_api(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    if (sse_decode_bool(deserializer)) {
      return (sse_decode_box_autoadd_extern_api(deserializer));
    } else {
      return null;
    }
  }

  @protected
  ExtraInfo? sse_decode_opt_box_autoadd_extra_info(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    if (sse_decode_bool(deserializer)) {
      return (sse_decode_box_autoadd_extra_info(deserializer));
    } else {
      return null;
    }
  }

  @protected
  MusicFuzzFilter? sse_decode_opt_box_autoadd_music_fuzz_filter(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    if (sse_decode_bool(deserializer)) {
      return (sse_decode_box_autoadd_music_fuzz_filter(deserializer));
    } else {
      return null;
    }
  }

  @protected
  Quality? sse_decode_opt_box_autoadd_quality(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    if (sse_decode_bool(deserializer)) {
      return (sse_decode_box_autoadd_quality(deserializer));
    } else {
      return null;
    }
  }

  @protected
  Release? sse_decode_opt_box_autoadd_release(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    if (sse_decode_bool(deserializer)) {
      return (sse_decode_box_autoadd_release(deserializer));
    } else {
      return null;
    }
  }

  @protected
  int? sse_decode_opt_box_autoadd_u_32(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    if (sse_decode_bool(deserializer)) {
      return (sse_decode_box_autoadd_u_32(deserializer));
    } else {
      return null;
    }
  }

  @protected
  PlayInfo sse_decode_play_info(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_uri = sse_decode_String(deserializer);
    var var_quality = sse_decode_quality(deserializer);
    return PlayInfo(uri: var_uri, quality: var_quality);
  }

  @protected
  Quality sse_decode_quality(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_short = sse_decode_String(deserializer);
    var var_level = sse_decode_opt_String(deserializer);
    var var_bitrate = sse_decode_opt_box_autoadd_u_32(deserializer);
    var var_format = sse_decode_opt_String(deserializer);
    var var_size = sse_decode_opt_String(deserializer);
    return Quality(
        short: var_short,
        level: var_level,
        bitrate: var_bitrate,
        format: var_format,
        size: var_size);
  }

  @protected
  (
    MusicListW,
    List<MusicAggregatorW>
  ) sse_decode_record_auto_owned_rust_opaque_flutter_rust_bridgefor_generated_rust_auto_opaque_inner_music_list_w_list_auto_owned_rust_opaque_flutter_rust_bridgefor_generated_rust_auto_opaque_inner_music_aggregator_w(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_field0 =
        sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
            deserializer);
    var var_field1 =
        sse_decode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
            deserializer);
    return (var_field0, var_field1);
  }

  @protected
  (String, String) sse_decode_record_string_string(
      SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_field0 = sse_decode_String(deserializer);
    var var_field1 = sse_decode_String(deserializer);
    return (var_field0, var_field1);
  }

  @protected
  Release sse_decode_release(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var var_url = sse_decode_String(deserializer);
    var var_assetsUrl = sse_decode_String(deserializer);
    var var_uploadUrl = sse_decode_String(deserializer);
    var var_htmlUrl = sse_decode_String(deserializer);
    var var_id = sse_decode_u_64(deserializer);
    var var_author = sse_decode_author(deserializer);
    var var_nodeId = sse_decode_String(deserializer);
    var var_tagName = sse_decode_String(deserializer);
    var var_targetCommitish = sse_decode_String(deserializer);
    var var_name = sse_decode_String(deserializer);
    var var_draft = sse_decode_bool(deserializer);
    var var_prerelease = sse_decode_bool(deserializer);
    var var_createdAt = sse_decode_String(deserializer);
    var var_publishedAt = sse_decode_String(deserializer);
    var var_assets = sse_decode_list_asset(deserializer);
    var var_tarballUrl = sse_decode_String(deserializer);
    var var_zipballUrl = sse_decode_String(deserializer);
    var var_body = sse_decode_String(deserializer);
    return Release(
        url: var_url,
        assetsUrl: var_assetsUrl,
        uploadUrl: var_uploadUrl,
        htmlUrl: var_htmlUrl,
        id: var_id,
        author: var_author,
        nodeId: var_nodeId,
        tagName: var_tagName,
        targetCommitish: var_targetCommitish,
        name: var_name,
        draft: var_draft,
        prerelease: var_prerelease,
        createdAt: var_createdAt,
        publishedAt: var_publishedAt,
        assets: var_assets,
        tarballUrl: var_tarballUrl,
        zipballUrl: var_zipballUrl,
        body: var_body);
  }

  @protected
  SqlFactoryW sse_decode_sql_factory_w(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return const SqlFactoryW();
  }

  @protected
  int sse_decode_u_32(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getUint32();
  }

  @protected
  BigInt sse_decode_u_64(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getBigUint64();
  }

  @protected
  int sse_decode_u_8(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getUint8();
  }

  @protected
  void sse_decode_unit(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  BigInt sse_decode_usize(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getBigUint64();
  }

  @protected
  int sse_decode_i_32(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getInt32();
  }

  @protected
  void sse_encode_AnyhowException(
      AnyhowException self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(self.message, serializer);
  }

  @protected
  void
      sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          MusicAggregatorW self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as MusicAggregatorWImpl).frbInternalSseEncode(move: true),
        serializer);
  }

  @protected
  void
      sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
          MusicListW self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as MusicListWImpl).frbInternalSseEncode(move: true), serializer);
  }

  @protected
  void
      sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          MusicW self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as MusicWImpl).frbInternalSseEncode(move: true), serializer);
  }

  @protected
  void
      sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          MusicAggregatorW self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as MusicAggregatorWImpl).frbInternalSseEncode(move: false),
        serializer);
  }

  @protected
  void
      sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          MusicAggregatorW self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as MusicAggregatorWImpl).frbInternalSseEncode(move: false),
        serializer);
  }

  @protected
  void
      sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
          MusicListW self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as MusicListWImpl).frbInternalSseEncode(move: false), serializer);
  }

  @protected
  void
      sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          MusicW self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as MusicWImpl).frbInternalSseEncode(move: false), serializer);
  }

  @protected
  void sse_encode_Chrono_Utc(DateTime self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_64(
        PlatformInt64Util.from(self.microsecondsSinceEpoch), serializer);
  }

  @protected
  void sse_encode_Map_String_String(
      Map<String, String> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_list_record_string_string(
        self.entries.map((e) => (e.key, e.value)).toList(), serializer);
  }

  @protected
  void
      sse_encode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          MusicAggregatorW self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as MusicAggregatorWImpl).frbInternalSseEncode(move: null),
        serializer);
  }

  @protected
  void
      sse_encode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
          MusicListW self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as MusicListWImpl).frbInternalSseEncode(move: null), serializer);
  }

  @protected
  void
      sse_encode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          MusicW self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize(
        (self as MusicWImpl).frbInternalSseEncode(move: null), serializer);
  }

  @protected
  void sse_encode_String(String self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_list_prim_u_8_strict(utf8.encoder.convert(self), serializer);
  }

  @protected
  void sse_encode_aggregator_online_factory_w(
      AggregatorOnlineFactoryW self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  void sse_encode_asset(Asset self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(self.url, serializer);
    sse_encode_u_64(self.id, serializer);
    sse_encode_String(self.nodeId, serializer);
    sse_encode_String(self.name, serializer);
    sse_encode_opt_String(self.label, serializer);
    sse_encode_author(self.uploader, serializer);
    sse_encode_String(self.contentType, serializer);
    sse_encode_String(self.state, serializer);
    sse_encode_u_64(self.size, serializer);
    sse_encode_u_64(self.downloadCount, serializer);
    sse_encode_String(self.createdAt, serializer);
    sse_encode_String(self.updatedAt, serializer);
    sse_encode_String(self.browserDownloadUrl, serializer);
  }

  @protected
  void sse_encode_author(Author self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(self.login, serializer);
    sse_encode_u_64(self.id, serializer);
    sse_encode_String(self.nodeId, serializer);
    sse_encode_String(self.avatarUrl, serializer);
    sse_encode_String(self.gravatarId, serializer);
    sse_encode_String(self.url, serializer);
    sse_encode_String(self.htmlUrl, serializer);
    sse_encode_String(self.followersUrl, serializer);
    sse_encode_String(self.followingUrl, serializer);
    sse_encode_String(self.gistsUrl, serializer);
    sse_encode_String(self.starredUrl, serializer);
    sse_encode_String(self.subscriptionsUrl, serializer);
    sse_encode_String(self.organizationsUrl, serializer);
    sse_encode_String(self.reposUrl, serializer);
    sse_encode_String(self.eventsUrl, serializer);
    sse_encode_String(self.receivedEventsUrl, serializer);
    sse_encode_String(self.type, serializer);
    sse_encode_bool(self.siteAdmin, serializer);
  }

  @protected
  void sse_encode_bool(bool self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putUint8(self ? 1 : 0);
  }

  @protected
  void
      sse_encode_box_autoadd_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          MusicW self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
        self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_Chrono_Utc(
      DateTime self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_Chrono_Utc(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_config(Config self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_config(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_extern_api(
      ExternApi self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_extern_api(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_extra_info(
      ExtraInfo self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_extra_info(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_music_fuzz_filter(
      MusicFuzzFilter self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_music_fuzz_filter(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_music_info(
      MusicInfo self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_music_info(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_music_list_info(
      MusicListInfo self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_music_list_info(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_play_info(
      PlayInfo self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_play_info(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_quality(Quality self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_quality(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_release(Release self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_release(self, serializer);
  }

  @protected
  void sse_encode_box_autoadd_u_32(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_u_32(self, serializer);
  }

  @protected
  void sse_encode_config(Config self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_bool(self.userAgreement, serializer);
    sse_encode_opt_box_autoadd_extern_api(self.externApi, serializer);
    sse_encode_bool(self.versionAutoUpdate, serializer);
    sse_encode_bool(self.externApiAutoUpdate, serializer);
    sse_encode_String(self.wifiAutoQuality, serializer);
    sse_encode_String(self.mobileAutoQuality, serializer);
    sse_encode_bool(self.savePicWhenAddMusicList, serializer);
    sse_encode_bool(self.saveLyricWhenAddMusicList, serializer);
    sse_encode_opt_String(self.exportCacheRoot, serializer);
    sse_encode_opt_String(self.lastExportCacheRoot, serializer);
    sse_encode_opt_String(self.externApiPath, serializer);
  }

  @protected
  void sse_encode_extern_api(ExternApi self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_opt_String(self.url, serializer);
    sse_encode_String(self.localPath, serializer);
    sse_encode_opt_String(self.lastHash, serializer);
    sse_encode_opt_box_autoadd_Chrono_Utc(self.lastModifiedTime, serializer);
  }

  @protected
  void sse_encode_extra_info(ExtraInfo self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_opt_box_autoadd_u_32(self.playCount, serializer);
    sse_encode_opt_box_autoadd_u_32(self.musicCount, serializer);
  }

  @protected
  void sse_encode_i_64(PlatformInt64 self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putPlatformInt64(self);
  }

  @protected
  void
      sse_encode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          List<MusicAggregatorW> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
          item, serializer);
    }
  }

  @protected
  void
      sse_encode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
          List<MusicListW> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
          item, serializer);
    }
  }

  @protected
  void
      sse_encode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          List<MusicW> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          item, serializer);
    }
  }

  @protected
  void sse_encode_list_String(List<String> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_String(item, serializer);
    }
  }

  @protected
  void sse_encode_list_asset(List<Asset> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_asset(item, serializer);
    }
  }

  @protected
  void sse_encode_list_music_info(
      List<MusicInfo> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_music_info(item, serializer);
    }
  }

  @protected
  void sse_encode_list_music_list_info(
      List<MusicListInfo> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_music_list_info(item, serializer);
    }
  }

  @protected
  void sse_encode_list_prim_i_64_strict(
      Int64List self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    serializer.buffer.putInt64List(self);
  }

  @protected
  void sse_encode_list_prim_u_8_strict(
      Uint8List self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    serializer.buffer.putUint8List(self);
  }

  @protected
  void sse_encode_list_quality(List<Quality> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_quality(item, serializer);
    }
  }

  @protected
  void sse_encode_list_record_string_string(
      List<(String, String)> self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    for (final item in self) {
      sse_encode_record_string_string(item, serializer);
    }
  }

  @protected
  void sse_encode_music_fuzz_filter(
      MusicFuzzFilter self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_opt_String(self.name, serializer);
    sse_encode_list_String(self.artist, serializer);
    sse_encode_opt_String(self.album, serializer);
  }

  @protected
  void sse_encode_music_info(MusicInfo self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_64(self.id, serializer);
    sse_encode_String(self.source, serializer);
    sse_encode_String(self.name, serializer);
    sse_encode_list_String(self.artist, serializer);
    sse_encode_opt_box_autoadd_u_32(self.duration, serializer);
    sse_encode_opt_String(self.album, serializer);
    sse_encode_list_quality(self.qualities, serializer);
    sse_encode_opt_box_autoadd_quality(self.defaultQuality, serializer);
    sse_encode_opt_String(self.artPic, serializer);
    sse_encode_opt_String(self.lyric, serializer);
  }

  @protected
  void sse_encode_music_list_info(
      MusicListInfo self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_64(self.id, serializer);
    sse_encode_String(self.name, serializer);
    sse_encode_String(self.artPic, serializer);
    sse_encode_String(self.desc, serializer);
    sse_encode_opt_box_autoadd_extra_info(self.extra, serializer);
  }

  @protected
  void sse_encode_online_factory_w(
      OnlineFactoryW self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  void sse_encode_opt_String(String? self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    sse_encode_bool(self != null, serializer);
    if (self != null) {
      sse_encode_String(self, serializer);
    }
  }

  @protected
  void
      sse_encode_opt_box_autoadd_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          MusicW? self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    sse_encode_bool(self != null, serializer);
    if (self != null) {
      sse_encode_box_autoadd_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicW(
          self, serializer);
    }
  }

  @protected
  void sse_encode_opt_box_autoadd_Chrono_Utc(
      DateTime? self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    sse_encode_bool(self != null, serializer);
    if (self != null) {
      sse_encode_box_autoadd_Chrono_Utc(self, serializer);
    }
  }

  @protected
  void sse_encode_opt_box_autoadd_extern_api(
      ExternApi? self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    sse_encode_bool(self != null, serializer);
    if (self != null) {
      sse_encode_box_autoadd_extern_api(self, serializer);
    }
  }

  @protected
  void sse_encode_opt_box_autoadd_extra_info(
      ExtraInfo? self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    sse_encode_bool(self != null, serializer);
    if (self != null) {
      sse_encode_box_autoadd_extra_info(self, serializer);
    }
  }

  @protected
  void sse_encode_opt_box_autoadd_music_fuzz_filter(
      MusicFuzzFilter? self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    sse_encode_bool(self != null, serializer);
    if (self != null) {
      sse_encode_box_autoadd_music_fuzz_filter(self, serializer);
    }
  }

  @protected
  void sse_encode_opt_box_autoadd_quality(
      Quality? self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    sse_encode_bool(self != null, serializer);
    if (self != null) {
      sse_encode_box_autoadd_quality(self, serializer);
    }
  }

  @protected
  void sse_encode_opt_box_autoadd_release(
      Release? self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    sse_encode_bool(self != null, serializer);
    if (self != null) {
      sse_encode_box_autoadd_release(self, serializer);
    }
  }

  @protected
  void sse_encode_opt_box_autoadd_u_32(int? self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs

    sse_encode_bool(self != null, serializer);
    if (self != null) {
      sse_encode_box_autoadd_u_32(self, serializer);
    }
  }

  @protected
  void sse_encode_play_info(PlayInfo self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(self.uri, serializer);
    sse_encode_quality(self.quality, serializer);
  }

  @protected
  void sse_encode_quality(Quality self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(self.short, serializer);
    sse_encode_opt_String(self.level, serializer);
    sse_encode_opt_box_autoadd_u_32(self.bitrate, serializer);
    sse_encode_opt_String(self.format, serializer);
    sse_encode_opt_String(self.size, serializer);
  }

  @protected
  void
      sse_encode_record_auto_owned_rust_opaque_flutter_rust_bridgefor_generated_rust_auto_opaque_inner_music_list_w_list_auto_owned_rust_opaque_flutter_rust_bridgefor_generated_rust_auto_opaque_inner_music_aggregator_w(
          (MusicListW, List<MusicAggregatorW>) self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicListW(
        self.$1, serializer);
    sse_encode_list_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerMusicAggregatorW(
        self.$2, serializer);
  }

  @protected
  void sse_encode_record_string_string(
      (String, String) self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(self.$1, serializer);
    sse_encode_String(self.$2, serializer);
  }

  @protected
  void sse_encode_release(Release self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_String(self.url, serializer);
    sse_encode_String(self.assetsUrl, serializer);
    sse_encode_String(self.uploadUrl, serializer);
    sse_encode_String(self.htmlUrl, serializer);
    sse_encode_u_64(self.id, serializer);
    sse_encode_author(self.author, serializer);
    sse_encode_String(self.nodeId, serializer);
    sse_encode_String(self.tagName, serializer);
    sse_encode_String(self.targetCommitish, serializer);
    sse_encode_String(self.name, serializer);
    sse_encode_bool(self.draft, serializer);
    sse_encode_bool(self.prerelease, serializer);
    sse_encode_String(self.createdAt, serializer);
    sse_encode_String(self.publishedAt, serializer);
    sse_encode_list_asset(self.assets, serializer);
    sse_encode_String(self.tarballUrl, serializer);
    sse_encode_String(self.zipballUrl, serializer);
    sse_encode_String(self.body, serializer);
  }

  @protected
  void sse_encode_sql_factory_w(SqlFactoryW self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  void sse_encode_u_32(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putUint32(self);
  }

  @protected
  void sse_encode_u_64(BigInt self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putBigUint64(self);
  }

  @protected
  void sse_encode_u_8(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putUint8(self);
  }

  @protected
  void sse_encode_unit(void self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  void sse_encode_usize(BigInt self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putBigUint64(self);
  }

  @protected
  void sse_encode_i_32(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putInt32(self);
  }
}

@sealed
class MusicAggregatorWImpl extends RustOpaque implements MusicAggregatorW {
  // Not to be used by end users
  MusicAggregatorWImpl.frbInternalDcoDecode(List<dynamic> wire)
      : super.frbInternalDcoDecode(wire, _kStaticData);

  // Not to be used by end users
  MusicAggregatorWImpl.frbInternalSseDecode(
      BigInt ptr, int externalSizeOnNative)
      : super.frbInternalSseDecode(ptr, externalSizeOnNative, _kStaticData);

  static final _kStaticData = RustArcStaticData(
    rustArcIncrementStrongCount:
        RustLib.instance.api.rust_arc_increment_strong_count_MusicAggregatorW,
    rustArcDecrementStrongCount:
        RustLib.instance.api.rust_arc_decrement_strong_count_MusicAggregatorW,
    rustArcDecrementStrongCountPtr: RustLib
        .instance.api.rust_arc_decrement_strong_count_MusicAggregatorWPtr,
  );

  Future<void> addMusic({required MusicW music}) => RustLib.instance.api
      .crateApiBindTypeBindMusicAggregatorWAddMusic(that: this, music: music);

  bool belongTo({required MusicW music}) => RustLib.instance.api
      .crateApiBindTypeBindMusicAggregatorWBelongTo(that: this, music: music);

  MusicAggregatorW clone() =>
      RustLib.instance.api.crateApiBindTypeBindMusicAggregatorWClone(
        that: this,
      );

  Future<(MusicListW, List<MusicAggregatorW>)> fetchAlbum(
          {required int page, required int limit}) =>
      RustLib.instance.api.crateApiBindTypeBindMusicAggregatorWFetchAlbum(
          that: this, page: page, limit: limit);

  Future<String> fetchLyric() =>
      RustLib.instance.api.crateApiBindTypeBindMusicAggregatorWFetchLyric(
        that: this,
      );

  Future<List<MusicW>> fetchMusics({required List<String> sources}) =>
      RustLib.instance.api.crateApiBindTypeBindMusicAggregatorWFetchMusics(
          that: this, sources: sources);

  List<MusicW> getAllMusics() =>
      RustLib.instance.api.crateApiBindTypeBindMusicAggregatorWGetAllMusics(
        that: this,
      );

  List<MusicW> getAllMusicsOwned() => RustLib.instance.api
          .crateApiBindTypeBindMusicAggregatorWGetAllMusicsOwned(
        that: this,
      );

  List<String> getAvailableSources() => RustLib.instance.api
          .crateApiBindTypeBindMusicAggregatorWGetAvailableSources(
        that: this,
      );

  MusicW getDefaultMusic() =>
      RustLib.instance.api.crateApiBindTypeBindMusicAggregatorWGetDefaultMusic(
        that: this,
      );

  String getDefaultSource() =>
      RustLib.instance.api.crateApiBindTypeBindMusicAggregatorWGetDefaultSource(
        that: this,
      );

  Future<MusicW?> getMusic({required String source}) => RustLib.instance.api
      .crateApiBindTypeBindMusicAggregatorWGetMusic(that: this, source: source);

  PlatformInt64 getMusicId() =>
      RustLib.instance.api.crateApiBindTypeBindMusicAggregatorWGetMusicId(
        that: this,
      );

  bool matchFilter({required MusicFuzzFilter filter}) =>
      RustLib.instance.api.crateApiBindTypeBindMusicAggregatorWMatchFilter(
          that: this, filter: filter);

  Future<void> setDefaultSource({required String source}) =>
      RustLib.instance.api.crateApiBindTypeBindMusicAggregatorWSetDefaultSource(
          that: this, source: source);

  String toString() =>
      RustLib.instance.api.crateApiBindTypeBindMusicAggregatorWToString(
        that: this,
      );
}

@sealed
class MusicListWImpl extends RustOpaque implements MusicListW {
  // Not to be used by end users
  MusicListWImpl.frbInternalDcoDecode(List<dynamic> wire)
      : super.frbInternalDcoDecode(wire, _kStaticData);

  // Not to be used by end users
  MusicListWImpl.frbInternalSseDecode(BigInt ptr, int externalSizeOnNative)
      : super.frbInternalSseDecode(ptr, externalSizeOnNative, _kStaticData);

  static final _kStaticData = RustArcStaticData(
    rustArcIncrementStrongCount:
        RustLib.instance.api.rust_arc_increment_strong_count_MusicListW,
    rustArcDecrementStrongCount:
        RustLib.instance.api.rust_arc_decrement_strong_count_MusicListW,
    rustArcDecrementStrongCountPtr:
        RustLib.instance.api.rust_arc_decrement_strong_count_MusicListWPtr,
  );

  Future<List<MusicAggregatorW>> fetchAllMusicAggregators(
          {required int pagesPerBatch,
          required int limit,
          required bool withLyric}) =>
      RustLib.instance.api
          .crateApiBindTypeBindMusicListWFetchAllMusicAggregators(
              that: this,
              pagesPerBatch: pagesPerBatch,
              limit: limit,
              withLyric: withLyric);

  Future<List<MusicAggregatorW>> getMusicAggregators(
          {required int page, required int limit}) =>
      RustLib.instance.api.crateApiBindTypeBindMusicListWGetMusicAggregators(
          that: this, page: page, limit: limit);

  MusicListInfo getMusiclistInfo() =>
      RustLib.instance.api.crateApiBindTypeBindMusicListWGetMusiclistInfo(
        that: this,
      );

  String source() => RustLib.instance.api.crateApiBindTypeBindMusicListWSource(
        that: this,
      );

  String toString() =>
      RustLib.instance.api.crateApiBindTypeBindMusicListWToString(
        that: this,
      );
}

@sealed
class MusicWImpl extends RustOpaque implements MusicW {
  // Not to be used by end users
  MusicWImpl.frbInternalDcoDecode(List<dynamic> wire)
      : super.frbInternalDcoDecode(wire, _kStaticData);

  // Not to be used by end users
  MusicWImpl.frbInternalSseDecode(BigInt ptr, int externalSizeOnNative)
      : super.frbInternalSseDecode(ptr, externalSizeOnNative, _kStaticData);

  static final _kStaticData = RustArcStaticData(
    rustArcIncrementStrongCount:
        RustLib.instance.api.rust_arc_increment_strong_count_MusicW,
    rustArcDecrementStrongCount:
        RustLib.instance.api.rust_arc_decrement_strong_count_MusicW,
    rustArcDecrementStrongCountPtr:
        RustLib.instance.api.rust_arc_decrement_strong_count_MusicWPtr,
  );

  Future<(MusicListW, List<MusicAggregatorW>)> fetchAlbum(
          {required int page, required int limit}) =>
      RustLib.instance.api.crateApiBindTypeBindMusicWFetchAlbum(
          that: this, page: page, limit: limit);

  Future<String> fetchLyric() =>
      RustLib.instance.api.crateApiBindTypeBindMusicWFetchLyric(
        that: this,
      );

  String getExtraInfo({required Quality quality}) => RustLib.instance.api
      .crateApiBindTypeBindMusicWGetExtraInfo(that: this, quality: quality);

  MusicInfo getMusicInfo() =>
      RustLib.instance.api.crateApiBindTypeBindMusicWGetMusicInfo(
        that: this,
      );

  (String, String) getPrimaryKv() =>
      RustLib.instance.api.crateApiBindTypeBindMusicWGetPrimaryKv(
        that: this,
      );

  String source() => RustLib.instance.api.crateApiBindTypeBindMusicWSource(
        that: this,
      );

  String toString() => RustLib.instance.api.crateApiBindTypeBindMusicWToString(
        that: this,
      );
}
