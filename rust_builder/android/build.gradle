// The Android Gradle Plugin builds the native code with the Android NDK.

group 'com.flutter_rust_bridge.rust_lib_app_rhyme'
version '1.0'

buildscript {
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        // The Android Gradle Plugin knows how to build native code with the NDK.
        classpath 'com.android.tools.build:gradle:7.3.0'
    }
}

rootProject.allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: 'com.android.library'

android {
    if (project.android.hasProperty("namespace")) {
        namespace 'com.flutter_rust_bridge.rust_lib_app_rhyme'
    }

    // Bumping the plugin compileSdkVersion requires all clients of this plugin
    // to bump the version in their app.
    compileSdkVersion 33

    // Use the NDK version
    // declared in /android/app/build.gradle file of the Flutter project.
    // Replace it with a version number if this plugin requires a specfic NDK version.
    // (e.g. ndkVersion "23.1.7779620")
    ndkVersion "22.1.7171670" 

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion 19
    }
}

apply from: "../cargokit/gradle/plugin.gradle"
cargokit {
    manifestDir = "../../rust"
    libname = "rust_lib_app_rhyme"
}
